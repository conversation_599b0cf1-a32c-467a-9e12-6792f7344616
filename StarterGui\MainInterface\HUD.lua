--[[
    Transport Empire - Main HUD Interface
    Creates and manages the main game overlay interface
--]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:Wait<PERSON><PERSON><PERSON>hil<PERSON>("PlayerGui")

-- HUD Manager
local HUDManager = {}

-- GUI Elements
local mainFrame
local moneyLabel
local timeLabel
local speedLabel
local pauseButton
local notificationFrame

-- Constants
local HUD_HEIGHT = 80
local NOTIFICATION_DURATION = 5

function HUDManager:Initialize()
    print("Initializing HUD...")
    
    -- Create main HUD frame
    self:CreateMainHUD()
    
    -- Connect to game state updates
    self:ConnectToGameState()
    
    -- Connect to player money updates
    self:ConnectToPlayerMoney()
    
    print("HUD initialized!")
end

function HUDManager:CreateMainHUD()
    -- Main HUD container
    mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainHUD"
    mainFrame.Size = UDim2.new(1, 0, 0, HUD_HEIGHT)
    mainFrame.Position = UDim2.new(0, 0, 0, 0)
    mainFrame.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    mainFrame.BackgroundTransparency = 0.1
    mainFrame.BorderSizePixel = 0
    mainFrame.Parent = playerGui
    
    -- Money display
    moneyLabel = Instance.new("TextLabel")
    moneyLabel.Name = "MoneyLabel"
    moneyLabel.Size = UDim2.new(0, 200, 1, 0)
    moneyLabel.Position = UDim2.new(0, 10, 0, 0)
    moneyLabel.BackgroundTransparency = 1
    moneyLabel.Text = "$500,000"
    moneyLabel.TextColor3 = Color3.fromRGB(100, 255, 100)
    moneyLabel.TextScaled = true
    moneyLabel.Font = Enum.Font.SourceSansBold
    moneyLabel.TextXAlignment = Enum.TextXAlignment.Left
    moneyLabel.Parent = mainFrame
    
    -- Time display
    timeLabel = Instance.new("TextLabel")
    timeLabel.Name = "TimeLabel"
    timeLabel.Size = UDim2.new(0, 150, 1, 0)
    timeLabel.Position = UDim2.new(0, 220, 0, 0)
    timeLabel.BackgroundTransparency = 1
    timeLabel.Text = "January 1850"
    timeLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    timeLabel.TextScaled = true
    timeLabel.Font = Enum.Font.SourceSans
    timeLabel.TextXAlignment = Enum.TextXAlignment.Left
    timeLabel.Parent = mainFrame
    
    -- Speed indicator
    speedLabel = Instance.new("TextLabel")
    speedLabel.Name = "SpeedLabel"
    speedLabel.Size = UDim2.new(0, 80, 1, 0)
    speedLabel.Position = UDim2.new(0, 380, 0, 0)
    speedLabel.BackgroundTransparency = 1
    speedLabel.Text = "1x"
    speedLabel.TextColor3 = Color3.fromRGB(255, 255, 100)
    speedLabel.TextScaled = true
    speedLabel.Font = Enum.Font.SourceSansBold
    speedLabel.TextXAlignment = Enum.TextXAlignment.Left
    speedLabel.Parent = mainFrame
    
    -- Pause button
    pauseButton = Instance.new("TextButton")
    pauseButton.Name = "PauseButton"
    pauseButton.Size = UDim2.new(0, 80, 0, 40)
    pauseButton.Position = UDim2.new(0, 470, 0, 20)
    pauseButton.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    pauseButton.BorderSizePixel = 1
    pauseButton.BorderColor3 = Color3.fromRGB(100, 100, 100)
    pauseButton.Text = "Pause"
    pauseButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    pauseButton.TextScaled = true
    pauseButton.Font = Enum.Font.SourceSans
    pauseButton.Parent = mainFrame
    
    -- Speed control buttons
    self:CreateSpeedControls()
    
    -- Notification area
    self:CreateNotificationArea()
    
    -- Tool palette
    self:CreateToolPalette()
end

function HUDManager:CreateSpeedControls()
    local speeds = {0.5, 1, 2, 4}
    local startX = 560
    
    for i, speed in ipairs(speeds) do
        local speedButton = Instance.new("TextButton")
        speedButton.Name = "Speed" .. speed .. "Button"
        speedButton.Size = UDim2.new(0, 40, 0, 30)
        speedButton.Position = UDim2.new(0, startX + (i-1) * 45, 0, 25)
        speedButton.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
        speedButton.BorderSizePixel = 1
        speedButton.BorderColor3 = Color3.fromRGB(80, 80, 80)
        speedButton.Text = speed .. "x"
        speedButton.TextColor3 = Color3.fromRGB(200, 200, 200)
        speedButton.TextScaled = true
        speedButton.Font = Enum.Font.SourceSans
        speedButton.Parent = mainFrame
        
        -- Connect button functionality (will be implemented in game manager)
        speedButton.MouseButton1Click:Connect(function()
            self:OnSpeedButtonClicked(speed)
        end)
    end
end

function HUDManager:CreateNotificationArea()
    notificationFrame = Instance.new("Frame")
    notificationFrame.Name = "NotificationArea"
    notificationFrame.Size = UDim2.new(0, 300, 0, 200)
    notificationFrame.Position = UDim2.new(1, -320, 0, HUD_HEIGHT + 10)
    notificationFrame.BackgroundTransparency = 1
    notificationFrame.Parent = playerGui
end

function HUDManager:CreateToolPalette()
    local toolPalette = Instance.new("Frame")
    toolPalette.Name = "ToolPalette"
    toolPalette.Size = UDim2.new(0, 60, 0, 400)
    toolPalette.Position = UDim2.new(0, 10, 0, HUD_HEIGHT + 10)
    toolPalette.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    toolPalette.BackgroundTransparency = 0.2
    toolPalette.BorderSizePixel = 1
    toolPalette.BorderColor3 = Color3.fromRGB(100, 100, 100)
    toolPalette.Parent = playerGui
    
    -- Tool buttons
    local tools = {
        {name = "Select", icon = "🔍"},
        {name = "Road", icon = "🛣️"},
        {name = "Rail", icon = "🚂"},
        {name = "Station", icon = "🏢"},
        {name = "Depot", icon = "🏭"},
        {name = "Demolish", icon = "💥"}
    }
    
    for i, tool in ipairs(tools) do
        local toolButton = Instance.new("TextButton")
        toolButton.Name = tool.name .. "Tool"
        toolButton.Size = UDim2.new(1, -10, 0, 50)
        toolButton.Position = UDim2.new(0, 5, 0, 5 + (i-1) * 55)
        toolButton.BackgroundColor3 = Color3.fromRGB(70, 70, 70)
        toolButton.BorderSizePixel = 1
        toolButton.BorderColor3 = Color3.fromRGB(120, 120, 120)
        toolButton.Text = tool.icon
        toolButton.TextColor3 = Color3.fromRGB(255, 255, 255)
        toolButton.TextScaled = true
        toolButton.Font = Enum.Font.SourceSans
        toolButton.Parent = toolPalette
        
        -- Add tool tip
        local toolTip = Instance.new("TextLabel")
        toolTip.Name = "ToolTip"
        toolTip.Size = UDim2.new(0, 80, 0, 30)
        toolTip.Position = UDim2.new(1, 10, 0, 10)
        toolTip.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
        toolTip.BackgroundTransparency = 0.3
        toolTip.BorderSizePixel = 0
        toolTip.Text = tool.name
        toolTip.TextColor3 = Color3.fromRGB(255, 255, 255)
        toolTip.TextScaled = true
        toolTip.Font = Enum.Font.SourceSans
        toolTip.Visible = false
        toolTip.Parent = toolButton
        
        -- Show/hide tooltip
        toolButton.MouseEnter:Connect(function()
            toolTip.Visible = true
        end)
        
        toolButton.MouseLeave:Connect(function()
            toolTip.Visible = false
        end)
        
        -- Connect tool functionality
        toolButton.MouseButton1Click:Connect(function()
            self:OnToolSelected(tool.name)
        end)
    end
end

function HUDManager:ConnectToGameState()
    -- Wait for game state to be created
    local gameState = ReplicatedStorage:WaitForChild("GameState")
    local timeFolder = gameState:WaitForChild("Time")
    
    -- Connect to time updates
    timeFolder.GameYear.Changed:Connect(function()
        self:UpdateTimeDisplay()
    end)
    
    timeFolder.GameMonth.Changed:Connect(function()
        self:UpdateTimeDisplay()
    end)
    
    timeFolder.IsPaused.Changed:Connect(function()
        self:UpdatePauseButton()
    end)
    
    timeFolder.TimeScale.Changed:Connect(function()
        self:UpdateSpeedDisplay()
    end)
end

function HUDManager:ConnectToPlayerMoney()
    -- Connect to player money updates
    local moneyValue = playerGui:WaitForChild("PlayerMoney")
    moneyValue.Changed:Connect(function()
        self:UpdateMoneyDisplay()
    end)
end

function HUDManager:UpdateMoneyDisplay()
    local moneyValue = playerGui:FindFirstChild("PlayerMoney")
    if moneyValue then
        local money = moneyValue.Value
        moneyLabel.Text = "$" .. self:FormatNumber(math.floor(money))
        
        -- Color based on money amount
        if money < 0 then
            moneyLabel.TextColor3 = Color3.fromRGB(255, 100, 100) -- Red for debt
        elseif money < 50000 then
            moneyLabel.TextColor3 = Color3.fromRGB(255, 255, 100) -- Yellow for low money
        else
            moneyLabel.TextColor3 = Color3.fromRGB(100, 255, 100) -- Green for good money
        end
    end
end

function HUDManager:UpdateTimeDisplay()
    local gameState = ReplicatedStorage:FindFirstChild("GameState")
    if gameState then
        local timeFolder = gameState:FindFirstChild("Time")
        if timeFolder then
            local year = timeFolder.GameYear.Value
            local month = timeFolder.GameMonth.Value
            
            local monthNames = {
                "January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"
            }
            
            timeLabel.Text = monthNames[month] .. " " .. year
        end
    end
end

function HUDManager:UpdatePauseButton()
    local gameState = ReplicatedStorage:FindFirstChild("GameState")
    if gameState then
        local timeFolder = gameState:FindFirstChild("Time")
        if timeFolder then
            local isPaused = timeFolder.IsPaused.Value
            pauseButton.Text = isPaused and "Resume" or "Pause"
            pauseButton.BackgroundColor3 = isPaused and Color3.fromRGB(100, 60, 60) or Color3.fromRGB(60, 60, 60)
        end
    end
end

function HUDManager:UpdateSpeedDisplay()
    local gameState = ReplicatedStorage:FindFirstChild("GameState")
    if gameState then
        local timeFolder = gameState:FindFirstChild("Time")
        if timeFolder then
            local timeScale = timeFolder.TimeScale.Value
            speedLabel.Text = timeScale .. "x"
        end
    end
end

function HUDManager:FormatNumber(number)
    -- Format large numbers with commas
    local formatted = tostring(number)
    local k = 0
    while k < #formatted do
        k = k + 1
        if (k % 3 == 1) and (k > 1) then
            formatted = formatted:sub(1, #formatted - k + 1) .. "," .. formatted:sub(#formatted - k + 2)
            k = k + 1
        end
    end
    return formatted
end

function HUDManager:ShowNotification(message, duration)
    duration = duration or NOTIFICATION_DURATION
    
    local notification = Instance.new("Frame")
    notification.Size = UDim2.new(1, 0, 0, 40)
    notification.Position = UDim2.new(0, 0, 0, 0)
    notification.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    notification.BackgroundTransparency = 0.2
    notification.BorderSizePixel = 1
    notification.BorderColor3 = Color3.fromRGB(120, 120, 120)
    notification.Parent = notificationFrame
    
    local messageLabel = Instance.new("TextLabel")
    messageLabel.Size = UDim2.new(1, -10, 1, 0)
    messageLabel.Position = UDim2.new(0, 5, 0, 0)
    messageLabel.BackgroundTransparency = 1
    messageLabel.Text = message
    messageLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    messageLabel.TextScaled = true
    messageLabel.Font = Enum.Font.SourceSans
    messageLabel.TextXAlignment = Enum.TextXAlignment.Left
    messageLabel.Parent = notification
    
    -- Animate in
    notification.Position = UDim2.new(1, 0, 0, 0)
    local tweenIn = TweenService:Create(notification, TweenInfo.new(0.3), {Position = UDim2.new(0, 0, 0, 0)})
    tweenIn:Play()
    
    -- Auto-remove after duration
    spawn(function()
        wait(duration)
        local tweenOut = TweenService:Create(notification, TweenInfo.new(0.3), {Position = UDim2.new(1, 0, 0, 0)})
        tweenOut:Play()
        tweenOut.Completed:Wait()
        notification:Destroy()
    end)
end

-- Event handlers (to be connected to game systems)
function HUDManager:OnSpeedButtonClicked(speed)
    -- This will be connected to the game manager
    print("Speed button clicked:", speed)
end

function HUDManager:OnToolSelected(toolName)
    -- This will be connected to the building system
    print("Tool selected:", toolName)
end

-- Initialize the HUD
HUDManager:Initialize()

return HUDManager
