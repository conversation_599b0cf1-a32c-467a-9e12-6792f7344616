--[[
    Transport Empire - Terrain Generator
    Generates procedural terrain based on map settings (Transport Fever 2 style)
--]]

print("TerrainGenerator script started!")

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")
local TweenService = game:GetService("TweenService")

-- Terrain Generator
local TerrainGenerator = {}

-- Constants
local REGION_SIZE = 512 -- Size of each terrain region
local WATER_LEVEL = 0.3 -- Base water level (0-1)
local MAX_HEIGHT = 100 -- Maximum terrain height

-- Noise settings for different features
local NOISE_SETTINGS = {
    -- Base terrain height
    height = {
        scale = 0.01,
        octaves = 4,
        persistence = 0.5,
        lacunarity = 2.0
    },
    -- Water features (rivers, lakes)
    water = {
        scale = 0.005,
        octaves = 2,
        persistence = 0.3,
        lacunarity = 2.0
    },
    -- Vegetation/biome
    biome = {
        scale = 0.02,
        octaves = 3,
        persistence = 0.4,
        lacunarity = 2.0
    }
}

-- Climate-specific settings
local CLIMATE_SETTINGS = {
    temperate = {
        baseHeight = 20,
        heightVariation = 40,
        waterMultiplier = 1.0,
        vegetationDensity = 0.7,
        materials = {
            Enum.Material.Grass,
            Enum.Material.Rock,
            Enum.Material.Water
        }
    },
    dry = {
        baseHeight = 30,
        heightVariation = 60,
        waterMultiplier = 0.3,
        vegetationDensity = 0.2,
        materials = {
            Enum.Material.Sand,
            Enum.Material.Rock,
            Enum.Material.Water
        }
    },
    tropical = {
        baseHeight = 10,
        heightVariation = 30,
        waterMultiplier = 1.5,
        vegetationDensity = 0.9,
        materials = {
            Enum.Material.LeafyGrass,
            Enum.Material.Sand,
            Enum.Material.Water
        }
    }
}

function TerrainGenerator:Initialize()
    print("Initializing Terrain Generator...")
    
    -- Set up terrain service
    self.terrain = Workspace.Terrain
    
    -- Initialize random seed
    self.currentSeed = 12345
    
    print("Terrain Generator initialized!")
end

function TerrainGenerator:GenerateMap(settings)
    print("Generating map with settings:", settings)
    
    -- Set seed for consistent generation
    if settings.seed and settings.seed ~= "" then
        self.currentSeed = tonumber(settings.seed) or 12345
    else
        self.currentSeed = math.random(100000, 999999)
    end
    
    math.randomseed(self.currentSeed)
    print("Using seed:", self.currentSeed)
    
    -- Get map size
    local mapSizes = {256, 512, 1024} -- Small, Medium, Large
    local mapSize = mapSizes[settings.mapSize] or 512
    
    -- Clear existing terrain
    self:ClearTerrain()
    
    -- Generate terrain based on settings
    self:GenerateTerrain(settings, mapSize)
    
    -- Place cities and industries
    self:PlaceCitiesAndIndustries(settings, mapSize)
    
    print("Map generation complete!")
    return true
end

function TerrainGenerator:ClearTerrain()
    print("Clearing existing terrain...")
    
    -- Clear all terrain in a large region
    local region = Region3.new(
        Vector3.new(-1000, -1000, -1000),
        Vector3.new(1000, 1000, 1000)
    )
    
    self.terrain:FillRegion(region, 4, Enum.Material.Air)
end

function TerrainGenerator:GenerateTerrain(settings, mapSize)
    print("Generating terrain for", settings.climate, "climate, size:", mapSize)
    
    local climateData = CLIMATE_SETTINGS[settings.climate] or CLIMATE_SETTINGS.temperate
    local halfSize = mapSize / 2
    
    -- Adjust height variation based on hilliness setting
    local hillinessMultipliers = {0.3, 1.0, 2.0} -- Flat, Normal, Very Hilly
    local heightVariation = climateData.heightVariation * hillinessMultipliers[settings.hilliness]
    
    -- Adjust water based on water setting
    local waterMultipliers = {0.3, 1.0, 2.0} -- Little, Normal, Lots
    local waterAmount = climateData.waterMultiplier * waterMultipliers[settings.water]
    
    -- Generate height map using noise
    local heightMap = self:GenerateHeightMap(mapSize, climateData.baseHeight, heightVariation)
    
    -- Generate water map
    local waterMap = self:GenerateWaterMap(mapSize, waterAmount)
    
    -- Create terrain regions
    self:CreateTerrainFromMaps(heightMap, waterMap, mapSize, climateData)
end

function TerrainGenerator:GenerateHeightMap(size, baseHeight, variation)
    print("Generating height map...")
    
    local heightMap = {}
    local noiseScale = NOISE_SETTINGS.height.scale
    
    for x = 1, size do
        heightMap[x] = {}
        for z = 1, size do
            -- Generate multi-octave noise for realistic terrain
            local height = 0
            local amplitude = 1
            local frequency = noiseScale
            
            for octave = 1, NOISE_SETTINGS.height.octaves do
                local noiseValue = math.noise(
                    (x + self.currentSeed) * frequency,
                    (z + self.currentSeed) * frequency,
                    self.currentSeed * 0.01
                )
                
                height = height + noiseValue * amplitude
                amplitude = amplitude * NOISE_SETTINGS.height.persistence
                frequency = frequency * NOISE_SETTINGS.height.lacunarity
            end
            
            -- Normalize and apply base height and variation
            height = (height + 1) / 2 -- Normalize to 0-1
            heightMap[x][z] = baseHeight + (height * variation)
        end
    end
    
    return heightMap
end

function TerrainGenerator:GenerateWaterMap(size, waterMultiplier)
    print("Generating water map...")
    
    local waterMap = {}
    local noiseScale = NOISE_SETTINGS.water.scale
    
    for x = 1, size do
        waterMap[x] = {}
        for z = 1, size do
            local waterNoise = math.noise(
                (x + self.currentSeed * 2) * noiseScale,
                (z + self.currentSeed * 2) * noiseScale,
                self.currentSeed * 0.02
            )
            
            -- Normalize and apply water multiplier
            waterNoise = (waterNoise + 1) / 2
            waterMap[x][z] = waterNoise * waterMultiplier
        end
    end
    
    return waterMap
end

function TerrainGenerator:CreateTerrainFromMaps(heightMap, waterMap, size, climateData)
    print("Creating terrain from height and water maps...")
    
    local resolution = 4 -- Terrain resolution
    local halfSize = size / 2
    
    -- Create terrain in chunks to avoid memory issues
    local chunkSize = 64
    
    for chunkX = 0, math.floor(size / chunkSize) do
        for chunkZ = 0, math.floor(size / chunkSize) do
            self:CreateTerrainChunk(
                heightMap, waterMap, 
                chunkX * chunkSize, chunkZ * chunkSize,
                math.min(chunkSize, size - chunkX * chunkSize),
                math.min(chunkSize, size - chunkZ * chunkSize),
                climateData, resolution, halfSize
            )
        end
    end
end

function TerrainGenerator:CreateTerrainChunk(heightMap, waterMap, startX, startZ, width, height, climateData, resolution, halfSize)
    -- Create region for this chunk
    local region = Region3.new(
        Vector3.new(startX - halfSize, -50, startZ - halfSize),
        Vector3.new(startX + width - halfSize, 150, startZ + height - halfSize)
    )
    
    -- Align region to terrain grid
    region = self.terrain:ExpandToGrid(region, resolution)
    
    -- Create material and occupancy arrays
    local material = {}
    local occupancy = {}
    
    local regionSize = region.Size
    local regionMin = region.CFrame.Position - regionSize/2
    
    -- Fill arrays based on height and water maps
    for x = 1, regionSize.X/resolution do
        material[x] = {}
        occupancy[x] = {}
        
        for y = 1, regionSize.Y/resolution do
            material[x][y] = {}
            occupancy[x][y] = {}
            
            for z = 1, regionSize.Z/resolution do
                local worldX = math.floor(regionMin.X + (x-1) * resolution + halfSize) + 1
                local worldY = regionMin.Y + (y-1) * resolution
                local worldZ = math.floor(regionMin.Z + (z-1) * resolution + halfSize) + 1
                
                -- Check bounds
                if worldX >= 1 and worldX <= #heightMap and worldZ >= 1 and worldZ <= #heightMap[1] then
                    local terrainHeight = heightMap[worldX][worldZ]
                    local waterLevel = waterMap[worldX][worldZ] * 20 -- Scale water level
                    
                    if worldY <= terrainHeight then
                        -- Solid terrain
                        if worldY <= waterLevel then
                            material[x][y][z] = climateData.materials[3] -- Water
                            occupancy[x][y][z] = 1
                        else
                            -- Choose material based on height
                            if worldY > terrainHeight - 10 then
                                material[x][y][z] = climateData.materials[1] -- Surface material
                            else
                                material[x][y][z] = climateData.materials[2] -- Rock/subsurface
                            end
                            occupancy[x][y][z] = 1
                        end
                    else
                        -- Air
                        material[x][y][z] = Enum.Material.Air
                        occupancy[x][y][z] = 0
                    end
                else
                    -- Outside bounds - air
                    material[x][y][z] = Enum.Material.Air
                    occupancy[x][y][z] = 0
                end
            end
        end
    end
    
    -- Apply to terrain
    self.terrain:FillRegion(region, resolution, material)
end

function TerrainGenerator:PlaceCitiesAndIndustries(settings, mapSize)
    print("Placing cities and industries...")

    -- Get density settings
    local cityDensities = {2, 5, 10} -- Few, Normal, Many
    local industryDensities = {1, 3, 6} -- Few, Normal, Many

    local numCities = cityDensities[settings.cities] or 5
    local numIndustries = industryDensities[settings.industries] or 3

    -- Scale based on map size
    local sizeMultipliers = {0.5, 1.0, 2.0} -- Small, Medium, Large
    local sizeMultiplier = sizeMultipliers[settings.mapSize] or 1.0

    numCities = math.floor(numCities * sizeMultiplier)
    numIndustries = math.floor(numIndustries * sizeMultiplier)

    print("Placing", numCities, "cities and", numIndustries, "industries")

    -- Place cities
    self:PlaceCities(numCities, mapSize, settings.climate)

    -- Place industries
    self:PlaceIndustries(numIndustries, mapSize, settings.climate)
end

function TerrainGenerator:PlaceCities(numCities, mapSize, climate)
    print("Placing", numCities, "cities...")

    local citiesFolder = Workspace:FindFirstChild("Cities")
    if not citiesFolder then
        citiesFolder = Instance.new("Folder")
        citiesFolder.Name = "Cities"
        citiesFolder.Parent = Workspace
    end

    -- Clear existing cities
    citiesFolder:ClearAllChildren()

    local halfSize = mapSize / 2
    local placedCities = {}

    for i = 1, numCities do
        local attempts = 0
        local placed = false

        while not placed and attempts < 50 do
            attempts = attempts + 1

            -- Random position
            local x = math.random(-halfSize + 50, halfSize - 50)
            local z = math.random(-halfSize + 50, halfSize - 50)

            -- Check if position is suitable (not too close to other cities)
            local suitable = true
            for _, cityPos in pairs(placedCities) do
                local distance = math.sqrt((x - cityPos.X)^2 + (z - cityPos.Z)^2)
                if distance < 100 then -- Minimum distance between cities
                    suitable = false
                    break
                end
            end

            if suitable then
                -- Get terrain height at this position
                local terrainHeight = self:GetTerrainHeightAt(x, z)
                local y = terrainHeight + 5

                -- Create city marker
                local city = self:CreateCityMarker(Vector3.new(x, y, z), "City " .. i, climate)
                city.Parent = citiesFolder

                -- Store position
                table.insert(placedCities, Vector3.new(x, y, z))
                placed = true

                print("Placed city", i, "at", x, z, "height", terrainHeight)
            end
        end
    end
end

function TerrainGenerator:PlaceIndustries(numIndustries, mapSize, climate)
    print("Placing", numIndustries, "industries...")

    local industriesFolder = Workspace:FindFirstChild("Industries")
    if not industriesFolder then
        industriesFolder = Instance.new("Folder")
        industriesFolder.Name = "Industries"
        industriesFolder.Parent = Workspace
    end

    -- Clear existing industries
    industriesFolder:ClearAllChildren()

    local halfSize = mapSize / 2
    local placedIndustries = {}

    -- Industry types based on climate
    local industryTypes = {
        temperate = {"Farm", "Lumber Mill", "Coal Mine", "Factory"},
        dry = {"Oil Well", "Quarry", "Cattle Ranch", "Mining"},
        tropical = {"Plantation", "Oil Palm", "Fishing", "Tourism"}
    }

    local types = industryTypes[climate] or industryTypes.temperate

    for i = 1, numIndustries do
        local attempts = 0
        local placed = false

        while not placed and attempts < 50 do
            attempts = attempts + 1

            -- Random position (prefer edges for industries)
            local x, z
            if math.random() < 0.7 then
                -- Place near edges
                if math.random() < 0.5 then
                    x = math.random() < 0.5 and math.random(-halfSize + 20, -halfSize + 100) or math.random(halfSize - 100, halfSize - 20)
                    z = math.random(-halfSize + 20, halfSize - 20)
                else
                    x = math.random(-halfSize + 20, halfSize - 20)
                    z = math.random() < 0.5 and math.random(-halfSize + 20, -halfSize + 100) or math.random(halfSize - 100, halfSize - 20)
                end
            else
                -- Random placement
                x = math.random(-halfSize + 50, halfSize - 50)
                z = math.random(-halfSize + 50, halfSize - 50)
            end

            -- Check if position is suitable
            local suitable = true
            for _, industryPos in pairs(placedIndustries) do
                local distance = math.sqrt((x - industryPos.X)^2 + (z - industryPos.Z)^2)
                if distance < 80 then -- Minimum distance between industries
                    suitable = false
                    break
                end
            end

            if suitable then
                -- Get terrain height at this position
                local terrainHeight = self:GetTerrainHeightAt(x, z)
                local y = terrainHeight + 5

                -- Choose industry type
                local industryType = types[math.random(1, #types)]

                -- Create industry marker
                local industry = self:CreateIndustryMarker(Vector3.new(x, y, z), industryType, climate)
                industry.Parent = industriesFolder

                -- Store position
                table.insert(placedIndustries, Vector3.new(x, y, z))
                placed = true

                print("Placed", industryType, "at", x, z, "height", terrainHeight)
            end
        end
    end
end

function TerrainGenerator:GetTerrainHeightAt(x, z)
    -- Simple terrain height sampling
    local ray = Workspace:Raycast(Vector3.new(x, 200, z), Vector3.new(0, -300, 0))
    if ray then
        return ray.Position.Y
    else
        return 0 -- Default height if no terrain found
    end
end

function TerrainGenerator:CreateCityMarker(position, name, climate)
    -- Create city marker (simple colored part for now)
    local city = Instance.new("Part")
    city.Name = name
    city.Size = Vector3.new(20, 10, 20)
    city.Position = position
    city.Anchored = true
    city.CanCollide = false
    city.Shape = Enum.PartType.Cylinder

    -- Color based on climate
    local cityColors = {
        temperate = Color3.fromRGB(100, 150, 100),
        dry = Color3.fromRGB(150, 120, 80),
        tropical = Color3.fromRGB(80, 150, 120)
    }
    city.Color = cityColors[climate] or cityColors.temperate

    -- Add label
    local billboard = Instance.new("BillboardGui")
    billboard.Size = UDim2.new(0, 100, 0, 50)
    billboard.StudsOffset = Vector3.new(0, 15, 0)
    billboard.Parent = city

    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = name
    label.TextColor3 = Color3.fromRGB(255, 255, 255)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSansBold
    label.TextStrokeTransparency = 0.5
    label.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
    label.Parent = billboard

    return city
end

function TerrainGenerator:CreateIndustryMarker(position, industryType, climate)
    -- Create industry marker (simple colored part for now)
    local industry = Instance.new("Part")
    industry.Name = industryType
    industry.Size = Vector3.new(15, 8, 15)
    industry.Position = position
    industry.Anchored = true
    industry.CanCollide = false
    industry.Shape = Enum.PartType.Block

    -- Color based on industry type
    local industryColors = {
        ["Farm"] = Color3.fromRGB(100, 200, 100),
        ["Lumber Mill"] = Color3.fromRGB(139, 69, 19),
        ["Coal Mine"] = Color3.fromRGB(50, 50, 50),
        ["Factory"] = Color3.fromRGB(150, 150, 150),
        ["Oil Well"] = Color3.fromRGB(100, 100, 100),
        ["Quarry"] = Color3.fromRGB(200, 200, 200),
        ["Cattle Ranch"] = Color3.fromRGB(160, 120, 80),
        ["Mining"] = Color3.fromRGB(120, 120, 120),
        ["Plantation"] = Color3.fromRGB(50, 150, 50),
        ["Oil Palm"] = Color3.fromRGB(80, 120, 50),
        ["Fishing"] = Color3.fromRGB(50, 100, 200),
        ["Tourism"] = Color3.fromRGB(200, 150, 100)
    }
    industry.Color = industryColors[industryType] or Color3.fromRGB(100, 100, 100)

    -- Add label
    local billboard = Instance.new("BillboardGui")
    billboard.Size = UDim2.new(0, 120, 0, 40)
    billboard.StudsOffset = Vector3.new(0, 12, 0)
    billboard.Parent = industry

    local label = Instance.new("TextLabel")
    label.Size = UDim2.new(1, 0, 1, 0)
    label.BackgroundTransparency = 1
    label.Text = industryType
    label.TextColor3 = Color3.fromRGB(255, 255, 255)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.TextStrokeTransparency = 0.5
    label.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
    label.Parent = billboard

    return industry
end

-- Initialize when script loads
TerrainGenerator:Initialize()

return TerrainGenerator
