--[[
    Transport Empire - Main Game Manager
    Handles core game loop, initialization, and coordination between systems
--]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local DataStoreService = game:GetService("DataStoreService")

-- Import game systems
local EconomySystem = require(script.Parent.EconomySystem.EconomyManager)
local TransportSystem = require(script.Parent.TransportSystem.TransportManager)
local CitySimulation = require(script.Parent.CitySimulation.CityManager)
local SaveSystem = require(script.Parent.SaveSystem.SaveManager)

-- Game Manager Class
local GameManager = {}
GameManager.__index = GameManager

-- Game State
local gameState = {
    isInitialized = false,
    isPaused = false,
    timeScale = 1,
    gameYear = 1850,
    gameMonth = 1,
    lastUpdateTime = 0,
    players = {}
}

-- Constants
local GAME_UPDATE_INTERVAL = 1 -- Update every second
local MONTHS_PER_YEAR = 12
local REAL_SECONDS_PER_GAME_MONTH = 30 -- 30 real seconds = 1 game month

function GameManager.new()
    local self = setmetatable({}, GameManager)
    return self
end

function GameManager:Initialize()
    print("Transport Empire - Initializing Game Manager...")
    
    -- Initialize game systems
    EconomySystem:Initialize()
    TransportSystem:Initialize()
    CitySimulation:Initialize()
    SaveSystem:Initialize()
    
    -- Set up game state in ReplicatedStorage
    self:CreateSharedGameState()
    
    -- Connect player events
    Players.PlayerAdded:Connect(function(player)
        self:OnPlayerJoined(player)
    end)
    
    Players.PlayerRemoving:Connect(function(player)
        self:OnPlayerLeaving(player)
    end)
    
    -- Start main game loop
    self:StartGameLoop()
    
    gameState.isInitialized = true
    print("Game Manager initialized successfully!")
end

function GameManager:CreateSharedGameState()
    -- Create shared game state for client access
    local sharedState = Instance.new("Folder")
    sharedState.Name = "GameState"
    sharedState.Parent = ReplicatedStorage
    
    -- Time values
    local timeFolder = Instance.new("Folder")
    timeFolder.Name = "Time"
    timeFolder.Parent = sharedState
    
    local yearValue = Instance.new("IntValue")
    yearValue.Name = "GameYear"
    yearValue.Value = gameState.gameYear
    yearValue.Parent = timeFolder
    
    local monthValue = Instance.new("IntValue")
    monthValue.Name = "GameMonth"
    monthValue.Value = gameState.gameMonth
    monthValue.Parent = timeFolder
    
    local pausedValue = Instance.new("BoolValue")
    pausedValue.Name = "IsPaused"
    pausedValue.Value = gameState.isPaused
    pausedValue.Parent = timeFolder
    
    local scaleValue = Instance.new("NumberValue")
    scaleValue.Name = "TimeScale"
    scaleValue.Value = gameState.timeScale
    scaleValue.Parent = timeFolder
end

function GameManager:StartGameLoop()
    -- Main game update loop
    spawn(function()
        while true do
            if not gameState.isPaused and gameState.isInitialized then
                local currentTime = tick()
                local deltaTime = currentTime - gameState.lastUpdateTime
                
                if deltaTime >= GAME_UPDATE_INTERVAL then
                    self:UpdateGame(deltaTime)
                    gameState.lastUpdateTime = currentTime
                end
            end
            
            wait(0.1) -- Small wait to prevent excessive CPU usage
        end
    end)
end

function GameManager:UpdateGame(deltaTime)
    -- Update game time
    self:UpdateGameTime(deltaTime)
    
    -- Update all game systems
    EconomySystem:Update(deltaTime)
    TransportSystem:Update(deltaTime)
    CitySimulation:Update(deltaTime)
    
    -- Update shared state
    self:UpdateSharedState()
end

function GameManager:UpdateGameTime(deltaTime)
    local timeProgress = (deltaTime * gameState.timeScale) / REAL_SECONDS_PER_GAME_MONTH
    
    -- Advance months
    gameState.gameMonth = gameState.gameMonth + timeProgress
    
    -- Handle year rollover
    if gameState.gameMonth >= MONTHS_PER_YEAR then
        gameState.gameYear = gameState.gameYear + 1
        gameState.gameMonth = gameState.gameMonth - MONTHS_PER_YEAR
        
        -- Trigger yearly events
        self:OnYearChanged()
    end
end

function GameManager:UpdateSharedState()
    local sharedState = ReplicatedStorage:FindFirstChild("GameState")
    if sharedState then
        local timeFolder = sharedState:FindFirstChild("Time")
        if timeFolder then
            timeFolder.GameYear.Value = math.floor(gameState.gameYear)
            timeFolder.GameMonth.Value = math.floor(gameState.gameMonth) + 1
            timeFolder.IsPaused.Value = gameState.isPaused
            timeFolder.TimeScale.Value = gameState.timeScale
        end
    end
end

function GameManager:OnPlayerJoined(player)
    print("Player joined:", player.Name)
    
    -- Initialize player data
    gameState.players[player.UserId] = {
        player = player,
        joinTime = tick(),
        money = 500000, -- Starting money
        vehicles = {},
        routes = {},
        research = {}
    }
    
    -- Load player save data
    SaveSystem:LoadPlayerData(player)
    
    -- Initialize player GUI
    self:InitializePlayerGUI(player)
end

function GameManager:OnPlayerLeaving(player)
    print("Player leaving:", player.Name)
    
    -- Save player data
    SaveSystem:SavePlayerData(player)
    
    -- Clean up player state
    gameState.players[player.UserId] = nil
end

function GameManager:InitializePlayerGUI(player)
    -- Wait for player's GUI to load
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Initialize main interface
    -- (GUI initialization will be handled by StarterGui scripts)
end

function GameManager:OnYearChanged()
    print("New year:", gameState.gameYear)
    
    -- Trigger yearly events for all systems
    EconomySystem:OnYearChanged(gameState.gameYear)
    CitySimulation:OnYearChanged(gameState.gameYear)
    TransportSystem:OnYearChanged(gameState.gameYear)
end

-- Public API functions
function GameManager:PauseGame()
    gameState.isPaused = true
    print("Game paused")
end

function GameManager:ResumeGame()
    gameState.isPaused = false
    gameState.lastUpdateTime = tick()
    print("Game resumed")
end

function GameManager:SetTimeScale(scale)
    gameState.timeScale = math.max(0.1, math.min(scale, 8)) -- Clamp between 0.1x and 8x
    print("Time scale set to:", gameState.timeScale)
end

function GameManager:GetGameState()
    return gameState
end

-- Initialize the game manager
local gameManager = GameManager.new()
gameManager:Initialize()

return GameManager
