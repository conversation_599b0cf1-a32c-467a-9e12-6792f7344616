--[[
    Transport Empire - Terrain Generation Handler
    Server-side handler for terrain generation requests
--]]

print("TerrainGenerationHandler script started!")

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

-- Load the terrain generator
local TerrainGenerator = require(script.Parent.TerrainGenerator)

-- Terrain Generation Handler
local TerrainGenerationHandler = {}

function TerrainGenerationHandler:Initialize()
    print("Initializing Terrain Generation Handler...")
    
    -- Wait for RemoteEvents to be created
    local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
    
    -- Create terrain generation remote event if it doesn't exist
    local generateTerrainEvent = remoteEvents:FindFirstChild("GenerateTerrain")
    if not generateTerrainEvent then
        generateTerrainEvent = Instance.new("RemoteEvent")
        generateTerrainEvent.Name = "GenerateTerrain"
        generateTerrainEvent.Parent = remoteEvents
    end
    
    -- Connect to terrain generation requests
    generateTerrainEvent.OnServerEvent:Connect(function(player, settings)
        self:HandleTerrainGeneration(player, settings)
    end)
    
    print("Terrain Generation Handler initialized!")
end

function TerrainGenerationHandler:HandleTerrainGeneration(player, settings)
    print("Handling terrain generation request from", player.Name)
    print("Settings:", settings)
    
    -- Validate settings
    if not self:ValidateSettings(settings) then
        print("Invalid settings received from", player.Name)
        return
    end
    
    -- Generate the terrain
    local success = pcall(function()
        TerrainGenerator:GenerateMap(settings)
    end)
    
    if success then
        print("Terrain generation completed successfully for", player.Name)
        
        -- Notify client that generation is complete
        local remoteEvents = ReplicatedStorage:FindFirstChild("RemoteEvents")
        local terrainCompleteEvent = remoteEvents:FindFirstChild("TerrainGenerationComplete")
        if not terrainCompleteEvent then
            terrainCompleteEvent = Instance.new("RemoteEvent")
            terrainCompleteEvent.Name = "TerrainGenerationComplete"
            terrainCompleteEvent.Parent = remoteEvents
        end
        
        terrainCompleteEvent:FireClient(player, {
            success = true,
            settings = settings
        })
    else
        print("Terrain generation failed for", player.Name)
        
        -- Notify client of failure
        local remoteEvents = ReplicatedStorage:FindFirstChild("RemoteEvents")
        local terrainCompleteEvent = remoteEvents:FindFirstChild("TerrainGenerationComplete")
        if terrainCompleteEvent then
            terrainCompleteEvent:FireClient(player, {
                success = false,
                error = "Terrain generation failed"
            })
        end
    end
end

function TerrainGenerationHandler:ValidateSettings(settings)
    -- Check required fields
    if not settings then
        return false
    end
    
    -- Validate climate
    if not settings.climate or not (settings.climate == "temperate" or settings.climate == "dry" or settings.climate == "tropical") then
        return false
    end
    
    -- Validate numeric settings (should be 1, 2, or 3)
    local numericSettings = {"mapSize", "cities", "industries", "water", "hilliness"}
    for _, setting in pairs(numericSettings) do
        if not settings[setting] or settings[setting] < 1 or settings[setting] > 3 then
            return false
        end
    end
    
    -- Seed can be anything (string or number)
    -- settings.seed is optional
    
    return true
end

-- Initialize when script loads
TerrainGenerationHandler:Initialize()

return TerrainGenerationHandler
