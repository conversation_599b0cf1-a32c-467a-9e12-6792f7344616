--[[
    Transport Empire - Map Generation Manager
    Creates the map generation setup interface (Transport Fever 2 style)
--]]

print("MapGenerationManager script started!")

local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local HttpService = game:GetService("HttpService")

local player = Players.LocalPlayer
local playerGui = player:Wait<PERSON><PERSON><PERSON><PERSON><PERSON>("PlayerGui")

-- Map Generation Manager
local MapGenerationManager = {}

-- GUI Elements
local mapGenGui
local mainFrame
local titleLabel
local climateFrame
local settingsFrame
local previewFrame
local navigationFrame

-- Current Settings
local currentSettings = {
    climate = "temperate", -- temperate, dry, tropical
    mapSize = 2, -- 1=small, 2=medium, 3=large
    cities = 2, -- 1=few, 2=normal, 3=many
    industries = 2, -- 1=few, 2=normal, 3=many
    water = 2, -- 1=little, 2=normal, 3=lots
    hilliness = 2, -- 1=flat, 2=normal, 3=very hilly
    seed = ""
}

-- Constants
local FADE_TIME = 0.5
local HOVER_TIME = 0.2

function MapGenerationManager:Initialize()
    print("Initializing Map Generation interface...")
    
    -- Generate random seed if none exists
    if currentSettings.seed == "" then
        self:GenerateRandomSeed()
    end
    
    -- Create map generation interface
    self:CreateMapGenerationInterface()
    
    print("Map Generation interface initialized!")
end

function MapGenerationManager:CreateMapGenerationInterface()
    -- Main ScreenGui container
    mapGenGui = Instance.new("ScreenGui")
    mapGenGui.Name = "MapGenerationGui"
    mapGenGui.Parent = playerGui
    
    -- Main frame (full screen)
    mainFrame = Instance.new("Frame")
    mainFrame.Name = "MapGenerationFrame"
    mainFrame.Size = UDim2.new(1, 0, 1, 0)
    mainFrame.Position = UDim2.new(0, 0, 0, 0)
    mainFrame.BackgroundColor3 = Color3.fromRGB(25, 30, 35)
    mainFrame.BorderSizePixel = 0
    mainFrame.BackgroundTransparency = 1 -- Start invisible for fade-in
    mainFrame.Parent = mapGenGui
    
    -- Background gradient
    local gradient = Instance.new("UIGradient")
    gradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(30, 40, 50)),
        ColorSequenceKeypoint.new(0.5, Color3.fromRGB(20, 25, 30)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(15, 20, 25))
    }
    gradient.Rotation = 45
    gradient.Parent = mainFrame
    
    -- Title
    titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "Title"
    titleLabel.Size = UDim2.new(0, 500, 0, 60)
    titleLabel.Position = UDim2.new(0.5, -250, 0.05, 0)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = "MAP GENERATION"
    titleLabel.TextColor3 = Color3.fromRGB(255, 220, 100)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.TextStrokeTransparency = 0.5
    titleLabel.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
    titleLabel.Parent = mainFrame
    
    -- Create sections
    self:CreateClimateSelection()
    self:CreateSettingsPanel()
    self:CreatePreviewPanel()
    self:CreateNavigationButtons()
end

function MapGenerationManager:CreateClimateSelection()
    -- Climate selection frame
    climateFrame = Instance.new("Frame")
    climateFrame.Name = "ClimateFrame"
    climateFrame.Size = UDim2.new(0, 600, 0, 200)
    climateFrame.Position = UDim2.new(0.1, 0, 0.15, 0)
    climateFrame.BackgroundColor3 = Color3.fromRGB(40, 50, 60)
    climateFrame.BorderSizePixel = 2
    climateFrame.BorderColor3 = Color3.fromRGB(80, 100, 120)
    climateFrame.Parent = mainFrame
    
    -- Climate title
    local climateTitle = Instance.new("TextLabel")
    climateTitle.Name = "ClimateTitle"
    climateTitle.Size = UDim2.new(1, 0, 0, 30)
    climateTitle.Position = UDim2.new(0, 0, 0, 5)
    climateTitle.BackgroundTransparency = 1
    climateTitle.Text = "CLIMATE SELECTION"
    climateTitle.TextColor3 = Color3.fromRGB(200, 200, 200)
    climateTitle.TextScaled = true
    climateTitle.Font = Enum.Font.SourceSansBold
    climateTitle.Parent = climateFrame
    
    -- Climate options
    local climateOptions = {
        {name = "temperate", display = "TEMPERATE", desc = "Green grasslands, rivers, mountains", color = Color3.fromRGB(50, 120, 50)},
        {name = "dry", display = "DRY", desc = "Canyons, deserts, plateaus", color = Color3.fromRGB(150, 100, 50)},
        {name = "tropical", display = "TROPICAL", desc = "Islands, palm trees, beaches", color = Color3.fromRGB(50, 150, 100)}
    }
    
    for i, climate in ipairs(climateOptions) do
        self:CreateClimateButton(climate, i, climateFrame)
    end
end

function MapGenerationManager:CreateClimateButton(climate, index, parent)
    local buttonWidth = 180
    local buttonHeight = 120
    local spacing = 10
    local startX = 10
    
    -- Climate button container
    local climateButton = Instance.new("Frame")
    climateButton.Name = climate.name .. "Button"
    climateButton.Size = UDim2.new(0, buttonWidth, 0, buttonHeight)
    climateButton.Position = UDim2.new(0, startX + (index - 1) * (buttonWidth + spacing), 0, 40)
    climateButton.BackgroundColor3 = climate.color
    climateButton.BorderSizePixel = 3
    climateButton.BorderColor3 = currentSettings.climate == climate.name and Color3.fromRGB(255, 255, 100) or Color3.fromRGB(100, 100, 100)
    climateButton.Parent = parent
    
    -- Climate preview (colored rectangle for now)
    local preview = Instance.new("Frame")
    preview.Name = "Preview"
    preview.Size = UDim2.new(1, -10, 0, 70)
    preview.Position = UDim2.new(0, 5, 0, 5)
    preview.BackgroundColor3 = climate.color
    preview.BorderSizePixel = 0
    preview.Parent = climateButton
    
    -- Climate name
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Name = "Name"
    nameLabel.Size = UDim2.new(1, 0, 0, 25)
    nameLabel.Position = UDim2.new(0, 0, 0, 75)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = climate.display
    nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.Parent = climateButton
    
    -- Climate description
    local descLabel = Instance.new("TextLabel")
    descLabel.Name = "Description"
    descLabel.Size = UDim2.new(1, 0, 0, 20)
    descLabel.Position = UDim2.new(0, 0, 0, 95)
    descLabel.BackgroundTransparency = 1
    descLabel.Text = climate.desc
    descLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    descLabel.TextScaled = true
    descLabel.Font = Enum.Font.SourceSans
    descLabel.Parent = climateButton
    
    -- Click detection
    local clickButton = Instance.new("TextButton")
    clickButton.Name = "ClickDetector"
    clickButton.Size = UDim2.new(1, 0, 1, 0)
    clickButton.Position = UDim2.new(0, 0, 0, 0)
    clickButton.BackgroundTransparency = 1
    clickButton.Text = ""
    clickButton.Parent = climateButton
    
    -- Click handler
    clickButton.MouseButton1Click:Connect(function()
        self:SelectClimate(climate.name)
    end)
    
    -- Hover effects
    clickButton.MouseEnter:Connect(function()
        climateButton.BorderColor3 = Color3.fromRGB(200, 200, 200)
    end)
    
    clickButton.MouseLeave:Connect(function()
        climateButton.BorderColor3 = currentSettings.climate == climate.name and Color3.fromRGB(255, 255, 100) or Color3.fromRGB(100, 100, 100)
    end)
end

function MapGenerationManager:SelectClimate(climateName)
    print("Selected climate:", climateName)
    currentSettings.climate = climateName
    
    -- Update all climate button borders
    for _, child in pairs(climateFrame:GetChildren()) do
        if child.Name:find("Button") then
            if child.Name:find(climateName) then
                child.BorderColor3 = Color3.fromRGB(255, 255, 100) -- Selected
            else
                child.BorderColor3 = Color3.fromRGB(100, 100, 100) -- Not selected
            end
        end
    end
    
    -- Update preview
    self:UpdatePreview()
end

function MapGenerationManager:CreateSettingsPanel()
    -- Settings panel frame
    settingsFrame = Instance.new("Frame")
    settingsFrame.Name = "SettingsFrame"
    settingsFrame.Size = UDim2.new(0, 600, 0, 300)
    settingsFrame.Position = UDim2.new(0.1, 0, 0.4, 0)
    settingsFrame.BackgroundColor3 = Color3.fromRGB(40, 50, 60)
    settingsFrame.BorderSizePixel = 2
    settingsFrame.BorderColor3 = Color3.fromRGB(80, 100, 120)
    settingsFrame.Parent = mainFrame
    
    -- Settings title
    local settingsTitle = Instance.new("TextLabel")
    settingsTitle.Name = "SettingsTitle"
    settingsTitle.Size = UDim2.new(1, 0, 0, 30)
    settingsTitle.Position = UDim2.new(0, 0, 0, 5)
    settingsTitle.BackgroundTransparency = 1
    settingsTitle.Text = "MAP SETTINGS"
    settingsTitle.TextColor3 = Color3.fromRGB(200, 200, 200)
    settingsTitle.TextScaled = true
    settingsTitle.Font = Enum.Font.SourceSansBold
    settingsTitle.Parent = settingsFrame
    
    -- Create sliders
    local sliderData = {
        {name = "mapSize", display = "Map Size", options = {"Small", "Medium", "Large"}},
        {name = "cities", display = "Cities", options = {"Few", "Normal", "Many"}},
        {name = "industries", display = "Industries", options = {"Few", "Normal", "Many"}},
        {name = "water", display = "Water", options = {"Little", "Normal", "Lots"}},
        {name = "hilliness", display = "Hilliness", options = {"Flat", "Normal", "Very Hilly"}}
    }
    
    for i, slider in ipairs(sliderData) do
        self:CreateSlider(slider, i, settingsFrame)
    end
    
    -- Seed input
    self:CreateSeedInput(settingsFrame)
end

function MapGenerationManager:CreateSlider(sliderData, index, parent)
    local yPos = 40 + (index - 1) * 45

    -- Slider label
    local label = Instance.new("TextLabel")
    label.Name = sliderData.name .. "Label"
    label.Size = UDim2.new(0, 120, 0, 30)
    label.Position = UDim2.new(0, 10, 0, yPos)
    label.BackgroundTransparency = 1
    label.Text = sliderData.display .. ":"
    label.TextColor3 = Color3.fromRGB(200, 200, 200)
    label.TextScaled = true
    label.Font = Enum.Font.SourceSans
    label.TextXAlignment = Enum.TextXAlignment.Left
    label.Parent = parent

    -- Slider buttons (3 options each)
    for i, option in ipairs(sliderData.options) do
        local button = Instance.new("TextButton")
        button.Name = sliderData.name .. "Option" .. i
        button.Size = UDim2.new(0, 80, 0, 25)
        button.Position = UDim2.new(0, 140 + (i - 1) * 85, 0, yPos + 2)
        button.BackgroundColor3 = currentSettings[sliderData.name] == i and Color3.fromRGB(100, 150, 200) or Color3.fromRGB(60, 70, 80)
        button.BorderSizePixel = 1
        button.BorderColor3 = Color3.fromRGB(120, 120, 120)
        button.Text = option
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSans
        button.Parent = parent

        -- Click handler
        button.MouseButton1Click:Connect(function()
            self:UpdateSetting(sliderData.name, i)
        end)

        -- Hover effects
        button.MouseEnter:Connect(function()
            if currentSettings[sliderData.name] ~= i then
                button.BackgroundColor3 = Color3.fromRGB(80, 90, 100)
            end
        end)

        button.MouseLeave:Connect(function()
            if currentSettings[sliderData.name] ~= i then
                button.BackgroundColor3 = Color3.fromRGB(60, 70, 80)
            end
        end)
    end
end

function MapGenerationManager:CreateSeedInput(parent)
    local yPos = 260

    -- Seed label
    local seedLabel = Instance.new("TextLabel")
    seedLabel.Name = "SeedLabel"
    seedLabel.Size = UDim2.new(0, 120, 0, 30)
    seedLabel.Position = UDim2.new(0, 10, 0, yPos)
    seedLabel.BackgroundTransparency = 1
    seedLabel.Text = "Seed:"
    seedLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    seedLabel.TextScaled = true
    seedLabel.Font = Enum.Font.SourceSans
    seedLabel.TextXAlignment = Enum.TextXAlignment.Left
    seedLabel.Parent = parent

    -- Seed input box
    local seedInput = Instance.new("TextBox")
    seedInput.Name = "SeedInput"
    seedInput.Size = UDim2.new(0, 200, 0, 30)
    seedInput.Position = UDim2.new(0, 140, 0, yPos)
    seedInput.BackgroundColor3 = Color3.fromRGB(60, 70, 80)
    seedInput.BorderSizePixel = 1
    seedInput.BorderColor3 = Color3.fromRGB(120, 120, 120)
    seedInput.Text = currentSettings.seed
    seedInput.TextColor3 = Color3.fromRGB(255, 255, 255)
    seedInput.TextScaled = true
    seedInput.Font = Enum.Font.SourceSans
    seedInput.PlaceholderText = "Enter seed or leave blank for random"
    seedInput.PlaceholderColor3 = Color3.fromRGB(150, 150, 150)
    seedInput.Parent = parent

    -- Random seed button
    local randomButton = Instance.new("TextButton")
    randomButton.Name = "RandomSeedButton"
    randomButton.Size = UDim2.new(0, 80, 0, 30)
    randomButton.Position = UDim2.new(0, 350, 0, yPos)
    randomButton.BackgroundColor3 = Color3.fromRGB(80, 120, 80)
    randomButton.BorderSizePixel = 1
    randomButton.BorderColor3 = Color3.fromRGB(120, 120, 120)
    randomButton.Text = "RANDOM"
    randomButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    randomButton.TextScaled = true
    randomButton.Font = Enum.Font.SourceSansBold
    randomButton.Parent = parent

    -- Seed input handler
    seedInput.FocusLost:Connect(function()
        currentSettings.seed = seedInput.Text
        self:UpdatePreview()
    end)

    -- Random button handler
    randomButton.MouseButton1Click:Connect(function()
        self:GenerateRandomSeed()
        seedInput.Text = currentSettings.seed
        self:UpdatePreview()
    end)
end

function MapGenerationManager:UpdateSetting(settingName, value)
    print("Updated", settingName, "to", value)
    currentSettings[settingName] = value

    -- Update button colors for this setting
    for _, child in pairs(settingsFrame:GetChildren()) do
        if child.Name:find(settingName .. "Option") then
            local optionNumber = tonumber(child.Name:sub(-1))
            if optionNumber == value then
                child.BackgroundColor3 = Color3.fromRGB(100, 150, 200) -- Selected
            else
                child.BackgroundColor3 = Color3.fromRGB(60, 70, 80) -- Not selected
            end
        end
    end

    -- Update preview
    self:UpdatePreview()
end

function MapGenerationManager:GenerateRandomSeed()
    -- Generate a random seed (simple number for now)
    currentSettings.seed = tostring(math.random(100000, 999999))
    print("Generated random seed:", currentSettings.seed)
end

function MapGenerationManager:CreatePreviewPanel()
    -- Preview panel frame
    previewFrame = Instance.new("Frame")
    previewFrame.Name = "PreviewFrame"
    previewFrame.Size = UDim2.new(0, 350, 0, 500)
    previewFrame.Position = UDim2.new(0.75, -175, 0.15, 0)
    previewFrame.BackgroundColor3 = Color3.fromRGB(40, 50, 60)
    previewFrame.BorderSizePixel = 2
    previewFrame.BorderColor3 = Color3.fromRGB(80, 100, 120)
    previewFrame.Parent = mainFrame

    -- Preview title
    local previewTitle = Instance.new("TextLabel")
    previewTitle.Name = "PreviewTitle"
    previewTitle.Size = UDim2.new(1, 0, 0, 30)
    previewTitle.Position = UDim2.new(0, 0, 0, 5)
    previewTitle.BackgroundTransparency = 1
    previewTitle.Text = "MAP PREVIEW"
    previewTitle.TextColor3 = Color3.fromRGB(200, 200, 200)
    previewTitle.TextScaled = true
    previewTitle.Font = Enum.Font.SourceSansBold
    previewTitle.Parent = previewFrame

    -- Preview image area (placeholder for now)
    local previewImage = Instance.new("Frame")
    previewImage.Name = "PreviewImage"
    previewImage.Size = UDim2.new(1, -20, 0, 300)
    previewImage.Position = UDim2.new(0, 10, 0, 40)
    previewImage.BackgroundColor3 = Color3.fromRGB(30, 40, 50)
    previewImage.BorderSizePixel = 1
    previewImage.BorderColor3 = Color3.fromRGB(100, 100, 100)
    previewImage.Parent = previewFrame

    -- Preview placeholder text
    local placeholderText = Instance.new("TextLabel")
    placeholderText.Name = "PlaceholderText"
    placeholderText.Size = UDim2.new(1, 0, 1, 0)
    placeholderText.Position = UDim2.new(0, 0, 0, 0)
    placeholderText.BackgroundTransparency = 1
    placeholderText.Text = "Map Preview\n(Generated based on settings)"
    placeholderText.TextColor3 = Color3.fromRGB(150, 150, 150)
    placeholderText.TextScaled = true
    placeholderText.Font = Enum.Font.SourceSans
    placeholderText.Parent = previewImage

    -- Settings summary
    local summaryLabel = Instance.new("TextLabel")
    summaryLabel.Name = "SummaryLabel"
    summaryLabel.Size = UDim2.new(1, -20, 0, 120)
    summaryLabel.Position = UDim2.new(0, 10, 0, 350)
    summaryLabel.BackgroundTransparency = 1
    summaryLabel.Text = "Settings Summary:\nClimate: Temperate\nSize: Medium\nCities: Normal\nIndustries: Normal\nWater: Normal\nHilliness: Normal"
    summaryLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    summaryLabel.TextScaled = true
    summaryLabel.Font = Enum.Font.SourceSans
    summaryLabel.TextXAlignment = Enum.TextXAlignment.Left
    summaryLabel.TextYAlignment = Enum.TextYAlignment.Top
    summaryLabel.Parent = previewFrame

    -- Update preview with current settings
    self:UpdatePreview()
end

function MapGenerationManager:CreateNavigationButtons()
    -- Navigation frame
    navigationFrame = Instance.new("Frame")
    navigationFrame.Name = "NavigationFrame"
    navigationFrame.Size = UDim2.new(1, 0, 0, 80)
    navigationFrame.Position = UDim2.new(0, 0, 0.9, 0)
    navigationFrame.BackgroundTransparency = 1
    navigationFrame.Parent = mainFrame

    -- Back button
    local backButton = Instance.new("TextButton")
    backButton.Name = "BackButton"
    backButton.Size = UDim2.new(0, 150, 0, 50)
    backButton.Position = UDim2.new(0, 50, 0.5, -25)
    backButton.BackgroundColor3 = Color3.fromRGB(80, 60, 60)
    backButton.BorderSizePixel = 2
    backButton.BorderColor3 = Color3.fromRGB(120, 100, 100)
    backButton.Text = "← BACK"
    backButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    backButton.TextScaled = true
    backButton.Font = Enum.Font.SourceSansBold
    backButton.Parent = navigationFrame

    -- Next button
    local nextButton = Instance.new("TextButton")
    nextButton.Name = "NextButton"
    nextButton.Size = UDim2.new(0, 150, 0, 50)
    nextButton.Position = UDim2.new(1, -200, 0.5, -25)
    nextButton.BackgroundColor3 = Color3.fromRGB(60, 120, 60)
    nextButton.BorderSizePixel = 2
    nextButton.BorderColor3 = Color3.fromRGB(100, 160, 100)
    nextButton.Text = "NEXT →"
    nextButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    nextButton.TextScaled = true
    nextButton.Font = Enum.Font.SourceSansBold
    nextButton.Parent = navigationFrame

    -- Button handlers
    backButton.MouseButton1Click:Connect(function()
        self:OnBackClicked()
    end)

    nextButton.MouseButton1Click:Connect(function()
        self:OnNextClicked()
    end)

    -- Hover effects
    backButton.MouseEnter:Connect(function()
        backButton.BackgroundColor3 = Color3.fromRGB(100, 80, 80)
    end)

    backButton.MouseLeave:Connect(function()
        backButton.BackgroundColor3 = Color3.fromRGB(80, 60, 60)
    end)

    nextButton.MouseEnter:Connect(function()
        nextButton.BackgroundColor3 = Color3.fromRGB(80, 140, 80)
    end)

    nextButton.MouseLeave:Connect(function()
        nextButton.BackgroundColor3 = Color3.fromRGB(60, 120, 60)
    end)
end

function MapGenerationManager:UpdatePreview()
    -- Update the settings summary
    local summaryFrame = previewFrame:FindFirstChild("SummaryLabel")
    if summaryFrame then
        local climateNames = {temperate = "Temperate", dry = "Dry", tropical = "Tropical"}
        local sizeNames = {"Small", "Medium", "Large"}
        local amountNames = {"Few", "Normal", "Many"}
        local waterNames = {"Little", "Normal", "Lots"}
        local hillNames = {"Flat", "Normal", "Very Hilly"}

        local summaryText = string.format(
            "Settings Summary:\nClimate: %s\nSize: %s\nCities: %s\nIndustries: %s\nWater: %s\nHilliness: %s\nSeed: %s",
            climateNames[currentSettings.climate] or "Unknown",
            sizeNames[currentSettings.mapSize] or "Unknown",
            amountNames[currentSettings.cities] or "Unknown",
            amountNames[currentSettings.industries] or "Unknown",
            waterNames[currentSettings.water] or "Unknown",
            hillNames[currentSettings.hilliness] or "Unknown",
            currentSettings.seed ~= "" and currentSettings.seed or "Random"
        )

        summaryFrame.Text = summaryText
    end

    -- Update preview image color based on climate
    local previewImage = previewFrame:FindFirstChild("PreviewImage")
    if previewImage then
        local climateColors = {
            temperate = Color3.fromRGB(50, 100, 50),
            dry = Color3.fromRGB(120, 80, 40),
            tropical = Color3.fromRGB(40, 120, 80)
        }
        previewImage.BackgroundColor3 = climateColors[currentSettings.climate] or Color3.fromRGB(30, 40, 50)
    end
end

-- Navigation handlers
function MapGenerationManager:OnBackClicked()
    print("Back to main menu...")

    -- Hide map generation interface
    local fadeOut = self:Hide()
    fadeOut.Completed:Connect(function()
        -- Show main menu again
        local MainMenuManager = require(playerGui:WaitForChild("MainMenuGui"):WaitForChild("MainMenuManager"))
        if MainMenuManager and MainMenuManager.Show then
            MainMenuManager:Show()
        end
    end)
end

function MapGenerationManager:OnNextClicked()
    print("Proceeding to game settings...")
    print("Current map settings:", currentSettings)

    -- Hide map generation interface
    local fadeOut = self:Hide()
    fadeOut.Completed:Connect(function()
        -- This will connect to Step 4: Game Settings Screen
        print("Ready to show Game Settings screen")
        -- TODO: Show game settings interface
    end)
end

-- Public API
function MapGenerationManager:Show()
    if mapGenGui then
        mapGenGui.Enabled = true
        self:ShowInterface()
    end
end

function MapGenerationManager:Hide()
    if mainFrame then
        return self:HideInterface()
    end
end

function MapGenerationManager:ShowInterface()
    -- Fade in the interface
    local fadeInTween = TweenService:Create(
        mainFrame,
        TweenInfo.new(FADE_TIME, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {BackgroundTransparency = 0}
    )
    fadeInTween:Play()
end

function MapGenerationManager:HideInterface()
    -- Fade out the interface
    local fadeOutTween = TweenService:Create(
        mainFrame,
        TweenInfo.new(FADE_TIME, Enum.EasingStyle.Quad, Enum.EasingDirection.In),
        {BackgroundTransparency = 1}
    )
    fadeOutTween:Play()

    fadeOutTween.Completed:Connect(function()
        if mapGenGui then
            mapGenGui.Enabled = false
        end
    end)

    return fadeOutTween
end

function MapGenerationManager:GetCurrentSettings()
    return currentSettings
end

function MapGenerationManager:SetSettings(newSettings)
    for key, value in pairs(newSettings) do
        if currentSettings[key] ~= nil then
            currentSettings[key] = value
        end
    end
    self:UpdatePreview()
end

-- Initialize when script loads (but don't show yet)
MapGenerationManager:Initialize()

return MapGenerationManager
