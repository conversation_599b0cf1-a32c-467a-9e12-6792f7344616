--[[
    Transport Empire - Economy System Manager
    Handles money, pricing, supply/demand, and economic calculations
--]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local EconomyManager = {}
EconomyManager.__index = EconomyManager

-- Economic constants
local CARGO_TYPES = {
    Passengers = {
        basePrice = 5,
        demandMultiplier = 1.2,
        distanceMultiplier = 0.1
    },
    Mail = {
        basePrice = 3,
        demandMultiplier = 1.0,
        distanceMultiplier = 0.05
    },
    Goods = {
        basePrice = 15,
        demandMultiplier = 1.5,
        distanceMultiplier = 0.2
    },
    RawMaterials = {
        basePrice = 10,
        demandMultiplier = 1.3,
        distanceMultiplier = 0.15
    }
}

local VEHICLE_COSTS = {
    -- Road vehicles
    Bus = { purchase = 25000, maintenance = 500 },
    Truck = { purchase = 35000, maintenance = 600 },
    Car = { purchase = 15000, maintenance = 300 },
    
    -- Rail vehicles
    SteamLocomotive = { purchase = 80000, maintenance = 1200 },
    PassengerCar = { purchase = 20000, maintenance = 400 },
    FreightCar = { purchase = 15000, maintenance = 300 },
    
    -- Water vehicles
    Steamship = { purchase = 150000, maintenance = 2000 },
    CargoShip = { purchase = 200000, maintenance = 2500 },
    Ferry = { purchase = 100000, maintenance = 1500 },
    
    -- Air vehicles
    Biplane = { purchase = 50000, maintenance = 800 },
    Airliner = { purchase = 500000, maintenance = 5000 },
    CargoPlane = { purchase = 600000, maintenance = 6000 }
}

local INFRASTRUCTURE_COSTS = {
    -- Road infrastructure
    RoadSegment = 1000,
    BusStop = 5000,
    TruckStation = 15000,
    
    -- Rail infrastructure
    RailSegment = 2000,
    TrainStation = 50000,
    RailDepot = 30000,
    
    -- Water infrastructure
    Harbor = 100000,
    Canal = 5000,
    
    -- Air infrastructure
    Airport = 500000,
    Helipad = 50000
}

-- Player economic data
local playerEconomies = {}

function EconomyManager:Initialize()
    print("Initializing Economy System...")
    
    -- Create shared economic data
    self:CreateSharedEconomicData()
    
    print("Economy System initialized!")
end

function EconomyManager:CreateSharedEconomicData()
    local economyFolder = Instance.new("Folder")
    economyFolder.Name = "Economy"
    economyFolder.Parent = ReplicatedStorage
    
    -- Cargo prices folder
    local pricesFolder = Instance.new("Folder")
    pricesFolder.Name = "CargoPrices"
    pricesFolder.Parent = economyFolder
    
    for cargoType, data in pairs(CARGO_TYPES) do
        local priceValue = Instance.new("NumberValue")
        priceValue.Name = cargoType
        priceValue.Value = data.basePrice
        priceValue.Parent = pricesFolder
    end
    
    -- Vehicle costs folder
    local costsFolder = Instance.new("Folder")
    costsFolder.Name = "VehicleCosts"
    costsFolder.Parent = economyFolder
    
    for vehicleType, costs in pairs(VEHICLE_COSTS) do
        local vehicleFolder = Instance.new("Folder")
        vehicleFolder.Name = vehicleType
        vehicleFolder.Parent = costsFolder
        
        local purchaseValue = Instance.new("NumberValue")
        purchaseValue.Name = "Purchase"
        purchaseValue.Value = costs.purchase
        purchaseValue.Parent = vehicleFolder
        
        local maintenanceValue = Instance.new("NumberValue")
        maintenanceValue.Name = "Maintenance"
        maintenanceValue.Value = costs.maintenance
        maintenanceValue.Parent = vehicleFolder
    end
end

function EconomyManager:InitializePlayerEconomy(player)
    playerEconomies[player.UserId] = {
        money = 500000, -- Starting money
        totalIncome = 0,
        totalExpenses = 0,
        monthlyIncome = 0,
        monthlyExpenses = 0,
        vehicles = {},
        routes = {},
        lastUpdate = tick()
    }
    
    -- Create player-specific economic data
    self:CreatePlayerEconomicData(player)
end

function EconomyManager:CreatePlayerEconomicData(player)
    local playerGui = player:WaitForChild("PlayerGui")
    
    -- Create money display value
    local moneyValue = Instance.new("NumberValue")
    moneyValue.Name = "PlayerMoney"
    moneyValue.Value = playerEconomies[player.UserId].money
    moneyValue.Parent = playerGui
end

function EconomyManager:Update(deltaTime)
    -- Update all player economies
    for userId, economy in pairs(playerEconomies) do
        self:UpdatePlayerEconomy(userId, deltaTime)
    end
    
    -- Update market prices based on supply/demand
    self:UpdateMarketPrices()
end

function EconomyManager:UpdatePlayerEconomy(userId, deltaTime)
    local economy = playerEconomies[userId]
    if not economy then return end
    
    local player = Players:GetPlayerByUserId(userId)
    if not player then return end
    
    -- Calculate vehicle maintenance costs
    local maintenanceCosts = self:CalculateMaintenanceCosts(economy.vehicles)
    
    -- Apply monthly expenses
    local monthlyExpenseRate = maintenanceCosts / (30 * 24 * 3600) -- Per second
    local expenseThisUpdate = monthlyExpenseRate * deltaTime
    
    economy.money = economy.money - expenseThisUpdate
    economy.totalExpenses = economy.totalExpenses + expenseThisUpdate
    economy.monthlyExpenses = economy.monthlyExpenses + expenseThisUpdate
    
    -- Update player GUI
    self:UpdatePlayerMoneyDisplay(player, economy.money)
end

function EconomyManager:CalculateMaintenanceCosts(vehicles)
    local totalCost = 0
    
    for _, vehicle in pairs(vehicles) do
        local vehicleType = vehicle.type
        if VEHICLE_COSTS[vehicleType] then
            totalCost = totalCost + VEHICLE_COSTS[vehicleType].maintenance
        end
    end
    
    return totalCost
end

function EconomyManager:UpdatePlayerMoneyDisplay(player, money)
    local playerGui = player:FindFirstChild("PlayerGui")
    if playerGui then
        local moneyValue = playerGui:FindFirstChild("PlayerMoney")
        if moneyValue then
            moneyValue.Value = money
        end
    end
end

function EconomyManager:UpdateMarketPrices()
    -- Simple supply/demand simulation
    -- In a full implementation, this would be based on actual cargo movement
    
    for cargoType, data in pairs(CARGO_TYPES) do
        -- Simulate market fluctuations (±10%)
        local fluctuation = (math.random() - 0.5) * 0.2
        local newPrice = data.basePrice * (1 + fluctuation)
        
        -- Update shared price data
        local economyFolder = ReplicatedStorage:FindFirstChild("Economy")
        if economyFolder then
            local pricesFolder = economyFolder:FindFirstChild("CargoPrices")
            if pricesFolder then
                local priceValue = pricesFolder:FindFirstChild(cargoType)
                if priceValue then
                    priceValue.Value = newPrice
                end
            end
        end
    end
end

function EconomyManager:CalculateCargoPrice(cargoType, origin, destination, amount)
    local cargoData = CARGO_TYPES[cargoType]
    if not cargoData then return 0 end
    
    -- Calculate distance
    local distance = (destination.Position - origin.Position).Magnitude
    
    -- Base price calculation
    local basePrice = cargoData.basePrice
    local distanceBonus = distance * cargoData.distanceMultiplier
    local totalPrice = (basePrice + distanceBonus) * amount
    
    return math.floor(totalPrice)
end

function EconomyManager:CanAfford(player, cost)
    local economy = playerEconomies[player.UserId]
    return economy and economy.money >= cost
end

function EconomyManager:SpendMoney(player, amount, description)
    local economy = playerEconomies[player.UserId]
    if not economy or economy.money < amount then
        return false
    end
    
    economy.money = economy.money - amount
    economy.totalExpenses = economy.totalExpenses + amount
    economy.monthlyExpenses = economy.monthlyExpenses + amount
    
    self:UpdatePlayerMoneyDisplay(player, economy.money)
    
    print(player.Name .. " spent $" .. amount .. " on " .. (description or "unknown"))
    return true
end

function EconomyManager:EarnMoney(player, amount, description)
    local economy = playerEconomies[player.UserId]
    if not economy then return false end
    
    economy.money = economy.money + amount
    economy.totalIncome = economy.totalIncome + amount
    economy.monthlyIncome = economy.monthlyIncome + amount
    
    self:UpdatePlayerMoneyDisplay(player, economy.money)
    
    print(player.Name .. " earned $" .. amount .. " from " .. (description or "unknown"))
    return true
end

function EconomyManager:GetPlayerMoney(player)
    local economy = playerEconomies[player.UserId]
    return economy and economy.money or 0
end

function EconomyManager:GetVehicleCost(vehicleType, costType)
    local costs = VEHICLE_COSTS[vehicleType]
    return costs and costs[costType] or 0
end

function EconomyManager:GetInfrastructureCost(infrastructureType)
    return INFRASTRUCTURE_COSTS[infrastructureType] or 0
end

function EconomyManager:OnYearChanged(year)
    -- Reset monthly statistics
    for userId, economy in pairs(playerEconomies) do
        economy.monthlyIncome = 0
        economy.monthlyExpenses = 0
    end
    
    print("Economy: New year " .. year .. " - Monthly stats reset")
end

-- Connect player events
game.Players.PlayerAdded:Connect(function(player)
    EconomyManager:InitializePlayerEconomy(player)
end)

game.Players.PlayerRemoving:Connect(function(player)
    playerEconomies[player.UserId] = nil
end)

return EconomyManager
