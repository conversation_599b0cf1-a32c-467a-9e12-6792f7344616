TERRAIN CONFIGURATION
====================

Terrain Object Settings:
- Size: 2048x2048 studs (large map)
- Material: Grass (primary), Rock, Sand, Water
- Decoration: Enabled
- CastShadow: true

Elevation Settings:
- Max Height: 200 studs
- Min Height: -50 studs (for water bodies)
- Smoothness: Medium
- Noise Scale: 0.1

Water Bodies:
- Rivers: 10-20 studs wide, flowing between cities
- Lakes: Various sizes for scenic beauty and ship transport
- Ocean: Surrounding map edges for international shipping
- Water Transparency: 0.3
- Water Color: Color3.fromRGB(100, 150, 200)

Environmental Features:
- Trees: Scattered across terrain, density varies by region
- Rocks: Mountain and hill areas
- Grass: Covering most flat areas
- Flowers: Decorative elements near cities

Lighting Configuration:
- Lighting Technology: Future
- Ambient: Color3.fromRGB(70, 70, 70)
- Brightness: 2
- ColorShift_Bottom: Color3.fromRGB(100, 100, 100)
- ColorShift_Top: Color3.fromRGB(200, 200, 200)
- EnvironmentDiffuseScale: 0.5
- EnvironmentSpecularScale: 0.5
- GlobalShadows: true
- OutdoorAmbient: Color3.fromRGB(127, 127, 127)
- ShadowSoftness: 0.2

Atmosphere Settings:
- Density: 0.3
- Offset: 0.25
- Color: Color3.fromRGB(199, 199, 199)
- Decay: Color3.fromRGB(92, 60, 13)
- Glare: 0.02
- Haze: 1.7
