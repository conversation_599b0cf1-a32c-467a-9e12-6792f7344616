--[[
    Transport Empire - Game Initializer
    Sets up the client-side game environment and disables default Roblox features
--]]

print("GameInitializer script started!")

local Players = game:GetService("Players")
local StarterGui = game:GetService("StarterGui")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer
print("GameInitializer - Player found:", player.Name)

-- Game Initializer
local GameInitializer = {}

function GameInitializer:Initialize()
    print("Initializing Transport Empire client...")
    
    -- Disable default Roblox UI elements
    self:DisableDefaultUI()
    
    -- Set up camera for simulation game
    self:SetupCamera()
    
    -- Wait for character and set up player
    self:SetupPlayer()
    
    print("Transport Empire client initialized!")
end

function GameInitializer:DisableDefaultUI()
    -- Disable default Roblox UI elements that we don't need
    StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.PlayerList, false)
    StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.Health, false)
    StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.Backpack, false)
    StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.Chat, true) -- Keep chat for multiplayer
    StarterGui:SetCoreGuiEnabled(Enum.CoreGuiType.EmotesMenu, false)
    
    -- Disable reset character option (players shouldn't reset in a simulation game)
    StarterGui:SetCore("ResetButtonCallback", false)
    
    print("Default Roblox UI disabled")
end

function GameInitializer:SetupCamera()
    -- Wait for camera
    local camera = workspace.CurrentCamera
    if not camera then
        workspace:GetPropertyChangedSignal("CurrentCamera"):Wait()
        camera = workspace.CurrentCamera
    end
    
    -- Set camera type for simulation game (free camera)
    camera.CameraType = Enum.CameraType.Scriptable
    
    -- Set initial camera position (overview of the map)
    camera.CFrame = CFrame.new(Vector3.new(0, 200, 0), Vector3.new(0, 0, 0))
    camera.FieldOfView = 70
    
    print("Camera configured for simulation game")
end

function GameInitializer:SetupPlayer()
    -- Wait for character to spawn
    local character = player.CharacterAdded:Wait()
    
    -- Hide the player character (we don't need it visible in a simulation game)
    if character then
        for _, part in pairs(character:GetChildren()) do
            if part:IsA("BasePart") then
                part.Transparency = 1
            elseif part:IsA("Accessory") then
                local handle = part:FindFirstChild("Handle")
                if handle then
                    handle.Transparency = 1
                end
            end
        end
        
        -- Disable character movement
        local humanoid = character:FindFirstChild("Humanoid")
        if humanoid then
            humanoid.PlatformStand = true
            humanoid.WalkSpeed = 0
            humanoid.JumpPower = 0
        end
        
        -- Position character out of the way
        local rootPart = character:FindFirstChild("HumanoidRootPart")
        if rootPart then
            rootPart.CFrame = CFrame.new(Vector3.new(0, -1000, 0))
            rootPart.Anchored = true
        end
    end
    
    print("Player character configured")
end

-- Initialize when script loads
GameInitializer:Initialize()

return GameInitializer
