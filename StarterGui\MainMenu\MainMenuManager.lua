--[[
    Transport Empire - Main Menu Manager
    Creates and manages the main menu interface (Transport Fever 2 style)
--]]

print("MainMenuManager script started!")

local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
print("Player found:", player.Name)

local playerGui = player:WaitFor<PERSON>hild("PlayerGui")
print("PlayerGui found!")

-- Simple test - create a basic GUI immediately
local testFrame = Instance.new("ScreenGui")
testFrame.Name = "MainMenuTest"
testFrame.Parent = playerGui

local testLabel = Instance.new("TextLabel")
testLabel.Size = UDim2.new(0, 400, 0, 100)
testLabel.Position = UDim2.new(0.5, -200, 0.5, -50)
testLabel.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
testLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
testLabel.Text = "TRANSPORT EMPIRE - MAIN MENU TEST"
testLabel.TextScaled = true
testLabel.Font = Enum.Font.SourceSansBold
testLabel.Parent = testFrame

print("Test GUI created!")

-- Main Menu Manager
local MainMenuManager = {}

-- GUI Elements
local mainMenuFrame
local titleLabel
local freeGameButton
local loadGameButton
local settingsButton
local exitButton
local backgroundImage

-- Constants
local MENU_FADE_TIME = 0.5
local BUTTON_HOVER_TIME = 0.2

function MainMenuManager:Initialize()
    print("Initializing Main Menu...")

    -- Remove test GUI
    if testFrame then
        testFrame:Destroy()
    end

    -- Create main menu interface
    self:CreateMainMenu()

    -- Show menu with fade-in effect
    self:ShowMenu()

    print("Main Menu initialized!")
end

function MainMenuManager:CreateMainMenu()
    -- Main menu ScreenGui container
    local mainMenuGui = Instance.new("ScreenGui")
    mainMenuGui.Name = "MainMenuGui"
    mainMenuGui.Parent = playerGui

    -- Main menu frame (full screen)
    mainMenuFrame = Instance.new("Frame")
    mainMenuFrame.Name = "MainMenu"
    mainMenuFrame.Size = UDim2.new(1, 0, 1, 0)
    mainMenuFrame.Position = UDim2.new(0, 0, 0, 0)
    mainMenuFrame.BackgroundColor3 = Color3.fromRGB(20, 25, 30)
    mainMenuFrame.BorderSizePixel = 0
    mainMenuFrame.BackgroundTransparency = 1 -- Start invisible for fade-in
    mainMenuFrame.Parent = mainMenuGui
    
    -- Background image/pattern (simulated with gradient)
    backgroundImage = Instance.new("Frame")
    backgroundImage.Name = "Background"
    backgroundImage.Size = UDim2.new(1, 0, 1, 0)
    backgroundImage.Position = UDim2.new(0, 0, 0, 0)
    backgroundImage.BackgroundColor3 = Color3.fromRGB(15, 20, 25)
    backgroundImage.BorderSizePixel = 0
    backgroundImage.Parent = mainMenuFrame
    
    -- Add gradient effect
    local gradient = Instance.new("UIGradient")
    gradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(25, 35, 45)),
        ColorSequenceKeypoint.new(0.5, Color3.fromRGB(15, 20, 25)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(10, 15, 20))
    }
    gradient.Rotation = 45
    gradient.Parent = backgroundImage
    
    -- Game title
    titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "GameTitle"
    titleLabel.Size = UDim2.new(0, 600, 0, 120)
    titleLabel.Position = UDim2.new(0.5, -300, 0.15, 0)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = "TRANSPORT EMPIRE"
    titleLabel.TextColor3 = Color3.fromRGB(255, 220, 100)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.SourceSansBold
    titleLabel.TextStrokeTransparency = 0.5
    titleLabel.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
    titleLabel.Parent = mainMenuFrame
    
    -- Subtitle
    local subtitleLabel = Instance.new("TextLabel")
    subtitleLabel.Name = "Subtitle"
    subtitleLabel.Size = UDim2.new(0, 400, 0, 40)
    subtitleLabel.Position = UDim2.new(0.5, -200, 0.28, 0)
    subtitleLabel.BackgroundTransparency = 1
    subtitleLabel.Text = "Build Your Transportation Network"
    subtitleLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    subtitleLabel.TextScaled = true
    subtitleLabel.Font = Enum.Font.SourceSans
    subtitleLabel.Parent = mainMenuFrame
    
    -- Create menu buttons
    self:CreateMenuButtons()
    
    -- Version label
    local versionLabel = Instance.new("TextLabel")
    versionLabel.Name = "Version"
    versionLabel.Size = UDim2.new(0, 200, 0, 30)
    versionLabel.Position = UDim2.new(1, -210, 1, -40)
    versionLabel.BackgroundTransparency = 1
    versionLabel.Text = "Version 1.0.0"
    versionLabel.TextColor3 = Color3.fromRGB(120, 120, 120)
    versionLabel.TextScaled = true
    versionLabel.Font = Enum.Font.SourceSans
    versionLabel.TextXAlignment = Enum.TextXAlignment.Right
    versionLabel.Parent = mainMenuFrame
end

function MainMenuManager:CreateMenuButtons()
    local buttonData = {
        {
            name = "FreeGame",
            text = "FREE GAME",
            description = "Start a new game on a procedurally generated map",
            position = UDim2.new(0.5, -150, 0.45, 0),
            callback = function() self:OnFreeGameClicked() end
        },
        {
            name = "LoadGame", 
            text = "LOAD GAME",
            description = "Continue a previously saved game",
            position = UDim2.new(0.5, -150, 0.55, 0),
            callback = function() self:OnLoadGameClicked() end
        },
        {
            name = "Settings",
            text = "SETTINGS",
            description = "Configure game options and preferences",
            position = UDim2.new(0.5, -150, 0.65, 0),
            callback = function() self:OnSettingsClicked() end
        },
        {
            name = "Exit",
            text = "EXIT",
            description = "Exit the game",
            position = UDim2.new(0.5, -150, 0.75, 0),
            callback = function() self:OnExitClicked() end
        }
    }
    
    for _, buttonInfo in ipairs(buttonData) do
        self:CreateMenuButton(buttonInfo)
    end
end

function MainMenuManager:CreateMenuButton(buttonInfo)
    -- Button container
    local buttonFrame = Instance.new("Frame")
    buttonFrame.Name = buttonInfo.name .. "Container"
    buttonFrame.Size = UDim2.new(0, 300, 0, 60)
    buttonFrame.Position = buttonInfo.position
    buttonFrame.BackgroundColor3 = Color3.fromRGB(40, 50, 60)
    buttonFrame.BorderSizePixel = 2
    buttonFrame.BorderColor3 = Color3.fromRGB(100, 120, 140)
    buttonFrame.Parent = mainMenuFrame
    
    -- Button gradient
    local buttonGradient = Instance.new("UIGradient")
    buttonGradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(50, 60, 70)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(30, 40, 50))
    }
    buttonGradient.Rotation = 90
    buttonGradient.Parent = buttonFrame
    
    -- Actual button
    local button = Instance.new("TextButton")
    button.Name = buttonInfo.name .. "Button"
    button.Size = UDim2.new(1, 0, 1, 0)
    button.Position = UDim2.new(0, 0, 0, 0)
    button.BackgroundTransparency = 1
    button.Text = buttonInfo.text
    button.TextColor3 = Color3.fromRGB(255, 255, 255)
    button.TextScaled = true
    button.Font = Enum.Font.SourceSansBold
    button.Parent = buttonFrame
    
    -- Description label (appears on hover)
    local descriptionLabel = Instance.new("TextLabel")
    descriptionLabel.Name = "Description"
    descriptionLabel.Size = UDim2.new(0, 400, 0, 30)
    descriptionLabel.Position = UDim2.new(1, 20, 0.5, -15)
    descriptionLabel.BackgroundTransparency = 1
    descriptionLabel.Text = buttonInfo.description
    descriptionLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    descriptionLabel.TextScaled = true
    descriptionLabel.Font = Enum.Font.SourceSans
    descriptionLabel.TextXAlignment = Enum.TextXAlignment.Left
    descriptionLabel.Visible = false
    descriptionLabel.Parent = buttonFrame
    
    -- Button hover effects
    button.MouseEnter:Connect(function()
        self:OnButtonHover(buttonFrame, descriptionLabel, true)
    end)
    
    button.MouseLeave:Connect(function()
        self:OnButtonHover(buttonFrame, descriptionLabel, false)
    end)
    
    -- Button click
    button.MouseButton1Click:Connect(function()
        self:OnButtonClick(buttonInfo.name)
        if buttonInfo.callback then
            buttonInfo.callback()
        end
    end)
    
    -- Store reference for later use
    if buttonInfo.name == "FreeGame" then
        freeGameButton = button
    elseif buttonInfo.name == "LoadGame" then
        loadGameButton = button
    elseif buttonInfo.name == "Settings" then
        settingsButton = button
    elseif buttonInfo.name == "Exit" then
        exitButton = button
    end
end

function MainMenuManager:OnButtonHover(buttonFrame, descriptionLabel, isHovering)
    local targetColor = isHovering and Color3.fromRGB(60, 70, 80) or Color3.fromRGB(40, 50, 60)
    local targetBorderColor = isHovering and Color3.fromRGB(150, 170, 190) or Color3.fromRGB(100, 120, 140)
    
    -- Animate button color change
    local colorTween = TweenService:Create(
        buttonFrame,
        TweenInfo.new(BUTTON_HOVER_TIME, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {BackgroundColor3 = targetColor, BorderColor3 = targetBorderColor}
    )
    colorTween:Play()
    
    -- Show/hide description
    descriptionLabel.Visible = isHovering
end

function MainMenuManager:OnButtonClick(buttonName)
    print("Menu button clicked:", buttonName)
    
    -- Add click sound effect (placeholder)
    -- In a real implementation, you'd play a sound here
end

function MainMenuManager:ShowMenu()
    -- Fade in the main menu
    local fadeInTween = TweenService:Create(
        mainMenuFrame,
        TweenInfo.new(MENU_FADE_TIME, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
        {BackgroundTransparency = 0}
    )
    fadeInTween:Play()
end

function MainMenuManager:HideMenu()
    -- Fade out the main menu
    local fadeOutTween = TweenService:Create(
        mainMenuFrame,
        TweenInfo.new(MENU_FADE_TIME, Enum.EasingStyle.Quad, Enum.EasingDirection.In),
        {BackgroundTransparency = 1}
    )
    fadeOutTween:Play()
    
    return fadeOutTween
end

-- Button event handlers
function MainMenuManager:OnFreeGameClicked()
    print("Starting Free Game mode...")

    -- Hide main menu and proceed to map generation
    local fadeOut = self:HideMenu()
    fadeOut.Completed:Connect(function()
        -- Show map generation interface
        print("Loading Map Generation screen...")

        -- Load the MapGenerationManager
        local mapGenScript = playerGui:FindFirstChild("MapGenerationGui")
        if not mapGenScript then
            -- Create the MapGenerationManager script if it doesn't exist
            print("MapGenerationManager not found - make sure it's properly loaded")
        else
            local MapGenerationManager = require(mapGenScript:FindFirstChild("MapGenerationManager"))
            if MapGenerationManager and MapGenerationManager.Show then
                MapGenerationManager:Show()
            end
        end
    end)
end

function MainMenuManager:OnLoadGameClicked()
    print("Opening Load Game screen...")
    
    -- Hide main menu and show load game interface
    local fadeOut = self:HideMenu()
    fadeOut.Completed:Connect(function()
        -- This will be implemented later
        print("Ready to show Load Game screen")
        -- TODO: Show load game interface
    end)
end

function MainMenuManager:OnSettingsClicked()
    print("Opening Settings screen...")
    
    -- Show settings overlay (don't hide main menu)
    print("Ready to show Settings overlay")
    -- TODO: Show settings interface
end

function MainMenuManager:OnExitClicked()
    print("Exit game requested...")
    
    -- In Roblox, we can't actually exit the game, but we can show a message
    print("In Roblox, players can close the tab to exit")
    -- TODO: Show exit confirmation dialog
end

-- Public API
function MainMenuManager:IsVisible()
    return mainMenuFrame and mainMenuFrame.BackgroundTransparency < 1
end

function MainMenuManager:Show()
    if mainMenuFrame then
        mainMenuFrame.Visible = true
        self:ShowMenu()
    end
end

function MainMenuManager:Hide()
    if mainMenuFrame then
        return self:HideMenu()
    end
end

-- Initialize the main menu when the script loads
wait(1) -- Small delay to ensure everything is ready
MainMenuManager:Initialize()

return MainMenuManager
