# Transport Empire - Game Design Document

## Game Overview

Transport Empire is a transportation simulation game inspired by Transport Fever 2, built for Roblox. Players build and manage transportation networks to connect cities and industries, facilitating the movement of passengers and cargo while growing their transportation empire.

## Core Gameplay Loop

1. **Analyze Demand**: Identify cities and industries needing connections
2. **Plan Routes**: Design efficient transportation networks
3. **Build Infrastructure**: Construct roads, railways, stations, and depots
4. **Purchase Vehicles**: Buy appropriate vehicles for each route
5. **Manage Operations**: Monitor performance and optimize routes
6. **Expand Empire**: Reinvest profits into new routes and technologies

## Game Systems Specification

### 1. Transportation Modes

#### Road Transport

- **Vehicle Types**: Buses (passengers), Trucks (cargo), Cars (mail/small cargo)
- **Infrastructure**: Roads, bus stops, truck stations, parking areas
- **Mechanics**: Traffic simulation, road capacity limits, maintenance costs

#### Rail Transport

- **Vehicle Types**: Passenger trains, freight trains, trams
- **Infrastructure**: Railway tracks, train stations, rail depots, signals
- **Mechanics**: Track switching, train scheduling, electrification

#### Water Transport

- **Vehicle Types**: Passenger ferries, cargo ships, barges
- **Infrastructure**: Harbors, docks, canals, lighthouses
- **Mechanics**: Water depth requirements, weather effects

#### Air Transport

- **Vehicle Types**: Passenger planes, cargo planes, helicopters
- **Infrastructure**: Airports, helipads, air traffic control
- **Mechanics**: Runway requirements, flight paths, fuel consumption

### 2. Economic System

#### Currency & Finances

- **Starting Capital**: $500,000 (configurable)
- **Income Sources**: Passenger fares, cargo delivery fees
- **Expenses**: Vehicle purchase/maintenance, infrastructure costs, fuel, staff wages
- **Loans**: Available for expansion with interest rates

#### Cargo Types & Pricing

- **Passengers**: $2-10 per trip based on distance
- **Mail**: $1-5 per unit, time-sensitive
- **Goods**: $5-20 per unit, various types (food, electronics, etc.)
- **Raw Materials**: $3-15 per unit (coal, oil, wood, etc.)

#### Dynamic Pricing

- Distance-based pricing multipliers
- Supply/demand affects rates
- Competition from other players (multiplayer)
- Seasonal variations for certain goods

### 3. City & Industry Simulation

#### City Growth Mechanics

- **Population Growth**: Based on transportation connectivity
- **Demand Generation**: More population = more passenger/cargo demand
- **Building Expansion**: Cities grow outward with better connections
- **Specialization**: Cities develop focus areas (residential, commercial, industrial)

#### Industry Buildings

- **Primary Industries**: Mines, farms, oil wells, forests
- **Secondary Industries**: Factories, refineries, mills
- **Tertiary Industries**: Shops, offices, services
- **Production Cycles**: Time-based resource generation and consumption

#### Supply Chains

- Raw materials → Factories → Finished goods → Cities
- Complex multi-step production chains
- Seasonal variations in production
- Quality and efficiency factors

### 4. Vehicle Management

#### Vehicle Properties

- **Capacity**: Passenger/cargo limits
- **Speed**: Maximum and cruising speeds
- **Range**: Fuel/battery limitations
- **Maintenance**: Reliability and repair costs
- **Age**: Performance degradation over time

#### Vehicle Lifecycle

- **Purchase**: From vehicle dealers with various options
- **Operation**: Route assignment and scheduling
- **Maintenance**: Regular servicing requirements
- **Upgrades**: Performance improvements available
- **Retirement**: Selling or scrapping old vehicles

### 5. Route Planning System

#### Route Creation

- **Point-to-Point**: Simple A to B connections
- **Multi-Stop**: Complex routes with multiple destinations
- **Circular Routes**: Loop-based transportation
- **Express Services**: Limited stops for faster travel

#### Scheduling & Automation

- **Timetables**: Fixed departure/arrival times
- **Frequency**: How often vehicles run routes
- **Load Balancing**: Multiple vehicles on busy routes
- **Conditional Logic**: Routes that adapt to demand

### 6. Research & Progression

#### Technology Tree

- **Early Era (1850-1900)**: Steam trains, horse carriages
- **Industrial Era (1900-1950)**: Early cars, electric trains
- **Modern Era (1950-2000)**: Jets, highways, containers
- **Future Era (2000+)**: High-speed rail, electric vehicles

#### Unlockable Content

- New vehicle types and models
- Advanced infrastructure options
- Improved efficiency technologies
- Specialized cargo handling equipment

## User Interface Specifications

### Main HUD Elements

- **Money Display**: Current funds and income/expense rates
- **Time Controls**: Pause, normal speed, fast forward
- **Tool Palette**: Building and management tools
- **Mini-Map**: Overview of transportation network
- **Notifications**: Important events and alerts

### Building Tools Interface

- **Infrastructure Menu**: Roads, rails, stations, depots
- **Terrain Tools**: Landscaping and modification
- **Demolition Tools**: Removing unwanted structures
- **Information Overlay**: Showing demand, capacity, profits

### Management Screens

- **Vehicle List**: All owned vehicles with status
- **Route Manager**: Creating and editing transportation routes
- **Financial Reports**: Detailed income/expense breakdown
- **Statistics**: Performance metrics and graphs
- **Research Tree**: Technology progression interface

## Implementation Priority

1. **Core Systems**: Economy, basic transportation, city simulation
2. **Essential UI**: Building tools, vehicle management, basic HUD
3. **Advanced Features**: Complex routing, research tree, multiplayer
4. **Polish**: Visual effects, audio, optimization

## Success Metrics

- Smooth 60 FPS performance with 100+ vehicles
- Intuitive building and management interface
- Engaging progression system keeping players for 2+ hours
- Stable multiplayer support for 4-8 players
- Complete feature parity with Transport Fever 2 core mechanics
