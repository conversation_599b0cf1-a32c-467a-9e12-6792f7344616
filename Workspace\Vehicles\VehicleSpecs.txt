VEHICLE SPECIFICATIONS
=====================

TRAIN VEHICLES
--------------
Steam Locomotive (1850):
- Body: 12x6x4 studs, BrickColor: Really black
- Smokestack: 1x4x1 studs, BrickColor: Dark stone grey
- Wheels: 2x2x2 studs cylinders, BrickColor: Really black
- Speed: 30 km/h, Capacity: 0 (locomotive only)
- Fuel: Coal, Range: 200 km

Passenger Car:
- Body: 10x6x3 studs, BrickColor: Bright red
- Windows: 0.2x2x1 studs, BrickColor: Light blue
- Doors: 2x3x0.2 studs, BrickColor: Brown
- Capacity: 50 passengers

Freight Car:
- Body: 10x6x4 studs, BrickColor: Brown
- Doors: 4x4x0.2 studs, BrickColor: Dark stone grey
- Capacity: 20 tons cargo

ROAD VEHICLES
-------------
Bus (1900):
- Body: 8x4x3 studs, BrickColor: Bright yellow
- Wheels: 1.5x1.5x1.5 studs, BrickColor: Really black
- Windows: 0.2x1x0.5 studs, BrickColor: Light blue
- Speed: 40 km/h, Capacity: 30 passengers
- Fuel: Gasoline, Range: 300 km

Truck (1920):
- Cab: 4x4x3 studs, BrickColor: Bright red
- Cargo Bed: 6x4x2 studs, BrickColor: Brown
- Wheels: 1.5x1.5x1.5 studs, BrickColor: Really black
- Speed: 50 km/h, Capacity: 15 tons
- Fuel: Diesel, Range: 500 km

Car (1930):
- Body: 6x3x2 studs, BrickColor: Bright blue
- Wheels: 1x1x1 studs, BrickColor: Really black
- Windows: 0.2x1x0.5 studs, BrickColor: Light blue
- Speed: 60 km/h, Capacity: 4 passengers + mail
- Fuel: Gasoline, Range: 400 km

WATER VEHICLES
--------------
Steamship (1850):
- Hull: 20x8x4 studs, BrickColor: Brown
- Smokestack: 2x8x2 studs, BrickColor: Really black
- Paddle Wheel: 4x4x1 studs, BrickColor: Brown
- Speed: 15 km/h, Capacity: 100 passengers or 50 tons
- Fuel: Coal, Range: 1000 km

Cargo Ship (1900):
- Hull: 30x10x6 studs, BrickColor: Dark stone grey
- Superstructure: 10x6x8 studs, BrickColor: White
- Cranes: 2x8x2 studs, BrickColor: Bright yellow
- Speed: 20 km/h, Capacity: 200 tons
- Fuel: Oil, Range: 2000 km

Ferry (1920):
- Hull: 15x8x3 studs, BrickColor: White
- Passenger Deck: 12x6x3 studs, BrickColor: Light stone grey
- Car Deck: 12x8x2 studs, BrickColor: Medium stone grey
- Speed: 25 km/h, Capacity: 50 passengers + 10 cars
- Fuel: Diesel, Range: 800 km

AIR VEHICLES
------------
Biplane (1920):
- Fuselage: 8x2x2 studs, BrickColor: Bright yellow
- Wings: 12x0.2x3 studs, BrickColor: White
- Propeller: 0.2x4x4 studs, BrickColor: Brown
- Speed: 120 km/h, Capacity: 2 passengers + mail
- Fuel: Gasoline, Range: 600 km

Airliner (1950):
- Fuselage: 20x4x4 studs, BrickColor: White
- Wings: 25x1x6 studs, BrickColor: Medium stone grey
- Engines: 3x3x2 studs, BrickColor: Dark stone grey
- Speed: 400 km/h, Capacity: 100 passengers
- Fuel: Jet fuel, Range: 3000 km

Cargo Plane (1960):
- Fuselage: 25x5x5 studs, BrickColor: Medium stone grey
- Wings: 30x1x8 studs, BrickColor: Dark stone grey
- Cargo Door: 4x4x0.2 studs, BrickColor: Really black
- Speed: 350 km/h, Capacity: 80 tons
- Fuel: Jet fuel, Range: 4000 km

VEHICLE PROPERTIES
-----------------
All vehicles should have:
- Anchored: false (for movement)
- CanCollide: true
- AssemblyLinearVelocity: Vector3.new(0, 0, 0)
- AssemblyAngularVelocity: Vector3.new(0, 0, 0)
- CustomPhysicalProperties: Density=0.7, Friction=0.5
- Proper collision groups
- Sound effects for engine/movement
- Particle effects for exhaust/steam

ANIMATION SYSTEM
---------------
- Use TweenService for smooth movement
- BodyVelocity for physics-based movement
- Rotate wheels/propellers during movement
- Particle effects for steam/exhaust
- Sound effects synchronized with movement
