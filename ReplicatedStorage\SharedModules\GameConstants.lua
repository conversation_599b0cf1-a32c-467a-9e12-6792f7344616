--[[
    Transport Empire - Game Constants
    Shared constants and configuration values used across client and server
--]]

local GameConstants = {}

-- Game Configuration
GameConstants.GAME_VERSION = "1.0.0"
GameConstants.STARTING_MONEY = 500000
GameConstants.STARTING_YEAR = 1850
GameConstants.MAX_PLAYERS = 8

-- Time System
GameConstants.REAL_SECONDS_PER_GAME_MONTH = 30
GameConstants.MONTHS_PER_YEAR = 12
GameConstants.MIN_TIME_SCALE = 0.1
GameConstants.MAX_TIME_SCALE = 8.0

-- Map Configuration
GameConstants.MAP_SIZE = 2048 -- studs
GameConstants.MAX_ELEVATION = 200 -- studs
GameConstants.MIN_ELEVATION = -50 -- studs (for water)

-- Transportation Types
GameConstants.TRANSPORT_TYPES = {
    ROAD = "Road",
    RAIL = "Rail",
    WATER = "Water",
    AIR = "Air"
}

-- Cargo Types
GameConstants.CARGO_TYPES = {
    PASSENGERS = "Passengers",
    MAIL = "Mail",
    GOODS = "Goods",
    RAW_MATERIALS = "RawMaterials"
}

-- Vehicle Categories
GameConstants.VEHICLE_CATEGORIES = {
    -- Road vehicles
    BUS = "Bus",
    TRUCK = "Truck",
    CAR = "Car",
    
    -- Rail vehicles
    STEAM_LOCOMOTIVE = "SteamLocomotive",
    PASSENGER_CAR = "PassengerCar",
    FREIGHT_CAR = "FreightCar",
    
    -- Water vehicles
    STEAMSHIP = "Steamship",
    CARGO_SHIP = "CargoShip",
    FERRY = "Ferry",
    
    -- Air vehicles
    BIPLANE = "Biplane",
    AIRLINER = "Airliner",
    CARGO_PLANE = "CargoPlane"
}

-- Infrastructure Types
GameConstants.INFRASTRUCTURE_TYPES = {
    -- Roads
    ROAD_SEGMENT = "RoadSegment",
    BUS_STOP = "BusStop",
    TRUCK_STATION = "TruckStation",
    
    -- Rails
    RAIL_SEGMENT = "RailSegment",
    TRAIN_STATION = "TrainStation",
    RAIL_DEPOT = "RailDepot",
    
    -- Water
    HARBOR = "Harbor",
    CANAL = "Canal",
    
    -- Air
    AIRPORT = "Airport",
    HELIPAD = "Helipad",
    
    -- Bridges and tunnels
    BRIDGE = "Bridge",
    TUNNEL = "Tunnel"
}

-- Economic Constants
GameConstants.ECONOMY = {
    -- Base cargo prices (per unit)
    CARGO_BASE_PRICES = {
        [GameConstants.CARGO_TYPES.PASSENGERS] = 5,
        [GameConstants.CARGO_TYPES.MAIL] = 3,
        [GameConstants.CARGO_TYPES.GOODS] = 15,
        [GameConstants.CARGO_TYPES.RAW_MATERIALS] = 10
    },
    
    -- Distance multipliers for pricing
    DISTANCE_MULTIPLIERS = {
        [GameConstants.CARGO_TYPES.PASSENGERS] = 0.1,
        [GameConstants.CARGO_TYPES.MAIL] = 0.05,
        [GameConstants.CARGO_TYPES.GOODS] = 0.2,
        [GameConstants.CARGO_TYPES.RAW_MATERIALS] = 0.15
    },
    
    -- Vehicle purchase costs
    VEHICLE_PURCHASE_COSTS = {
        [GameConstants.VEHICLE_CATEGORIES.BUS] = 25000,
        [GameConstants.VEHICLE_CATEGORIES.TRUCK] = 35000,
        [GameConstants.VEHICLE_CATEGORIES.CAR] = 15000,
        [GameConstants.VEHICLE_CATEGORIES.STEAM_LOCOMOTIVE] = 80000,
        [GameConstants.VEHICLE_CATEGORIES.PASSENGER_CAR] = 20000,
        [GameConstants.VEHICLE_CATEGORIES.FREIGHT_CAR] = 15000,
        [GameConstants.VEHICLE_CATEGORIES.STEAMSHIP] = 150000,
        [GameConstants.VEHICLE_CATEGORIES.CARGO_SHIP] = 200000,
        [GameConstants.VEHICLE_CATEGORIES.FERRY] = 100000,
        [GameConstants.VEHICLE_CATEGORIES.BIPLANE] = 50000,
        [GameConstants.VEHICLE_CATEGORIES.AIRLINER] = 500000,
        [GameConstants.VEHICLE_CATEGORIES.CARGO_PLANE] = 600000
    },
    
    -- Monthly maintenance costs
    VEHICLE_MAINTENANCE_COSTS = {
        [GameConstants.VEHICLE_CATEGORIES.BUS] = 500,
        [GameConstants.VEHICLE_CATEGORIES.TRUCK] = 600,
        [GameConstants.VEHICLE_CATEGORIES.CAR] = 300,
        [GameConstants.VEHICLE_CATEGORIES.STEAM_LOCOMOTIVE] = 1200,
        [GameConstants.VEHICLE_CATEGORIES.PASSENGER_CAR] = 400,
        [GameConstants.VEHICLE_CATEGORIES.FREIGHT_CAR] = 300,
        [GameConstants.VEHICLE_CATEGORIES.STEAMSHIP] = 2000,
        [GameConstants.VEHICLE_CATEGORIES.CARGO_SHIP] = 2500,
        [GameConstants.VEHICLE_CATEGORIES.FERRY] = 1500,
        [GameConstants.VEHICLE_CATEGORIES.BIPLANE] = 800,
        [GameConstants.VEHICLE_CATEGORIES.AIRLINER] = 5000,
        [GameConstants.VEHICLE_CATEGORIES.CARGO_PLANE] = 6000
    },
    
    -- Infrastructure construction costs
    INFRASTRUCTURE_COSTS = {
        [GameConstants.INFRASTRUCTURE_TYPES.ROAD_SEGMENT] = 1000,
        [GameConstants.INFRASTRUCTURE_TYPES.BUS_STOP] = 5000,
        [GameConstants.INFRASTRUCTURE_TYPES.TRUCK_STATION] = 15000,
        [GameConstants.INFRASTRUCTURE_TYPES.RAIL_SEGMENT] = 2000,
        [GameConstants.INFRASTRUCTURE_TYPES.TRAIN_STATION] = 50000,
        [GameConstants.INFRASTRUCTURE_TYPES.RAIL_DEPOT] = 30000,
        [GameConstants.INFRASTRUCTURE_TYPES.HARBOR] = 100000,
        [GameConstants.INFRASTRUCTURE_TYPES.CANAL] = 5000,
        [GameConstants.INFRASTRUCTURE_TYPES.AIRPORT] = 500000,
        [GameConstants.INFRASTRUCTURE_TYPES.HELIPAD] = 50000,
        [GameConstants.INFRASTRUCTURE_TYPES.BRIDGE] = 10000,
        [GameConstants.INFRASTRUCTURE_TYPES.TUNNEL] = 15000
    }
}

-- Vehicle Specifications
GameConstants.VEHICLE_SPECS = {
    [GameConstants.VEHICLE_CATEGORIES.BUS] = {
        capacity = 30,
        speed = 40, -- km/h
        cargoTypes = {GameConstants.CARGO_TYPES.PASSENGERS},
        fuelType = "Gasoline",
        range = 300 -- km
    },
    [GameConstants.VEHICLE_CATEGORIES.TRUCK] = {
        capacity = 15, -- tons
        speed = 50,
        cargoTypes = {GameConstants.CARGO_TYPES.GOODS, GameConstants.CARGO_TYPES.RAW_MATERIALS},
        fuelType = "Diesel",
        range = 500
    },
    [GameConstants.VEHICLE_CATEGORIES.CAR] = {
        capacity = 4,
        speed = 60,
        cargoTypes = {GameConstants.CARGO_TYPES.PASSENGERS, GameConstants.CARGO_TYPES.MAIL},
        fuelType = "Gasoline",
        range = 400
    },
    [GameConstants.VEHICLE_CATEGORIES.STEAM_LOCOMOTIVE] = {
        capacity = 0, -- Locomotive only
        speed = 30,
        cargoTypes = {},
        fuelType = "Coal",
        range = 200
    },
    [GameConstants.VEHICLE_CATEGORIES.PASSENGER_CAR] = {
        capacity = 50,
        speed = 0, -- Pulled by locomotive
        cargoTypes = {GameConstants.CARGO_TYPES.PASSENGERS},
        fuelType = "None",
        range = 0
    },
    [GameConstants.VEHICLE_CATEGORIES.FREIGHT_CAR] = {
        capacity = 20, -- tons
        speed = 0,
        cargoTypes = {GameConstants.CARGO_TYPES.GOODS, GameConstants.CARGO_TYPES.RAW_MATERIALS},
        fuelType = "None",
        range = 0
    }
}

-- City Configuration
GameConstants.CITY_CONFIG = {
    MIN_STARTING_POPULATION = 1000,
    MAX_STARTING_POPULATION = 5000,
    BASE_GROWTH_RATE = 0.01, -- 1% per month
    MAX_GROWTH_RATE = 0.05, -- 5% per month
    BUILDINGS_PER_POPULATION = 50, -- 1 building per 50 people
    
    -- Demand generation rates
    PASSENGER_DEMAND_RATE = 0.1, -- 10% of population travels per month
    MAIL_DEMAND_RATE = 0.05, -- 5% of population sends mail per month
    GOODS_DEMAND_RATE = 0.2 -- 20% of population buys goods per month
}

-- Research System
GameConstants.RESEARCH = {
    STARTING_RESEARCH_POINTS = 0,
    RESEARCH_POINTS_PER_YEAR = 100,
    
    -- Technology unlock years
    UNLOCK_YEARS = {
        -- Early era (1850-1900)
        [GameConstants.VEHICLE_CATEGORIES.STEAM_LOCOMOTIVE] = 1850,
        [GameConstants.VEHICLE_CATEGORIES.STEAMSHIP] = 1850,
        
        -- Industrial era (1900-1950)
        [GameConstants.VEHICLE_CATEGORIES.BUS] = 1900,
        [GameConstants.VEHICLE_CATEGORIES.TRUCK] = 1920,
        [GameConstants.VEHICLE_CATEGORIES.CAR] = 1930,
        [GameConstants.VEHICLE_CATEGORIES.BIPLANE] = 1920,
        
        -- Modern era (1950-2000)
        [GameConstants.VEHICLE_CATEGORIES.AIRLINER] = 1950,
        [GameConstants.VEHICLE_CATEGORIES.CARGO_PLANE] = 1960,
        [GameConstants.VEHICLE_CATEGORIES.FERRY] = 1920,
        
        -- Future era (2000+)
        -- High-speed rail, electric vehicles, etc.
    }
}

-- UI Configuration
GameConstants.UI = {
    HUD_HEIGHT = 80,
    TOOL_PALETTE_WIDTH = 60,
    NOTIFICATION_DURATION = 5,
    
    -- Colors
    COLORS = {
        BACKGROUND = Color3.fromRGB(40, 40, 40),
        PANEL = Color3.fromRGB(50, 50, 50),
        BUTTON = Color3.fromRGB(60, 60, 60),
        BUTTON_HOVER = Color3.fromRGB(80, 80, 80),
        TEXT = Color3.fromRGB(255, 255, 255),
        TEXT_SECONDARY = Color3.fromRGB(200, 200, 200),
        SUCCESS = Color3.fromRGB(100, 255, 100),
        WARNING = Color3.fromRGB(255, 255, 100),
        ERROR = Color3.fromRGB(255, 100, 100)
    }
}

-- Performance Configuration
GameConstants.PERFORMANCE = {
    MAX_VEHICLES_PER_PLAYER = 100,
    MAX_INFRASTRUCTURE_PIECES = 1000,
    UPDATE_FREQUENCY = 1, -- seconds
    LOD_DISTANCE = 500, -- studs
    CULLING_DISTANCE = 1000 -- studs
}

return GameConstants
