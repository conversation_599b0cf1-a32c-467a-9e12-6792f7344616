--[[
    Transport Empire - Remote Events Configuration
    Defines all RemoteEvents and RemoteFunctions for client-server communication
--]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create RemoteEvents folder if it doesn't exist
local remoteEventsFolder = ReplicatedStorage:FindFirstChild("RemoteEvents")
if not remoteEventsFolder then
    remoteEventsFolder = Instance.new("Folder")
    remoteEventsFolder.Name = "RemoteEvents"
    remoteEventsFolder.Parent = ReplicatedStorage
end

-- Game Events Configuration
local GameEvents = {}

-- Define all remote events and functions
local eventDefinitions = {
    -- Game Control Events
    {name = "PauseGame", type = "RemoteEvent", description = "Pause/resume the game"},
    {name = "SetTimeScale", type = "RemoteEvent", description = "Change game speed"},
    {name = "SaveGame", type = "RemoteEvent", description = "Save player progress"},
    
    -- Economy Events
    {name = "PurchaseVehicle", type = "RemoteEvent", description = "Buy a new vehicle"},
    {name = "SellVehicle", type = "RemoteEvent", description = "Sell an existing vehicle"},
    {name = "GetPlayerMoney", type = "RemoteFunction", description = "Get current player money"},
    {name = "TransactionComplete", type = "RemoteEvent", description = "Notify client of transaction"},
    
    -- Infrastructure Events
    {name = "BuildInfrastructure", type = "RemoteEvent", description = "Build roads, rails, etc."},
    {name = "DemolishInfrastructure", type = "RemoteEvent", description = "Remove infrastructure"},
    {name = "PlaceStation", type = "RemoteEvent", description = "Place stations and depots"},
    {name = "InfrastructureUpdate", type = "RemoteEvent", description = "Notify clients of infrastructure changes"},
    
    -- Vehicle Management Events
    {name = "CreateRoute", type = "RemoteEvent", description = "Create a new transportation route"},
    {name = "ModifyRoute", type = "RemoteEvent", description = "Modify existing route"},
    {name = "DeleteRoute", type = "RemoteEvent", description = "Delete a route"},
    {name = "AssignVehicleToRoute", type = "RemoteEvent", description = "Assign vehicle to route"},
    {name = "VehicleStatusUpdate", type = "RemoteEvent", description = "Update vehicle status"},
    
    -- City and Industry Events
    {name = "CityGrowth", type = "RemoteEvent", description = "Notify of city population changes"},
    {name = "IndustryProduction", type = "RemoteEvent", description = "Industry production updates"},
    {name = "DemandUpdate", type = "RemoteEvent", description = "Supply/demand changes"},
    
    -- Research Events
    {name = "UnlockTechnology", type = "RemoteEvent", description = "Unlock new technology"},
    {name = "ResearchProgress", type = "RemoteEvent", description = "Research point updates"},
    
    -- UI Events
    {name = "ShowNotification", type = "RemoteEvent", description = "Display notification to player"},
    {name = "UpdateUI", type = "RemoteEvent", description = "Update UI elements"},
    {name = "OpenManagementPanel", type = "RemoteEvent", description = "Open management interfaces"},
    
    -- Multiplayer Events
    {name = "PlayerJoined", type = "RemoteEvent", description = "Player joined notification"},
    {name = "PlayerLeft", type = "RemoteEvent", description = "Player left notification"},
    {name = "ChatMessage", type = "RemoteEvent", description = "In-game chat"},
    
    -- Statistics Events
    {name = "UpdateStatistics", type = "RemoteEvent", description = "Update game statistics"},
    {name = "GetStatistics", type = "RemoteFunction", description = "Get current statistics"},
    
    -- Tool Events
    {name = "SelectTool", type = "RemoteEvent", description = "Tool selection"},
    {name = "ToolAction", type = "RemoteEvent", description = "Tool usage"},
    {name = "CancelAction", type = "RemoteEvent", description = "Cancel current action"}
}

-- Create remote events and functions
function GameEvents:Initialize()
    print("Initializing Remote Events...")
    
    for _, eventDef in ipairs(eventDefinitions) do
        local existingEvent = remoteEventsFolder:FindFirstChild(eventDef.name)
        
        if not existingEvent then
            local remoteObject
            
            if eventDef.type == "RemoteEvent" then
                remoteObject = Instance.new("RemoteEvent")
            elseif eventDef.type == "RemoteFunction" then
                remoteObject = Instance.new("RemoteFunction")
            end
            
            if remoteObject then
                remoteObject.Name = eventDef.name
                remoteObject.Parent = remoteEventsFolder
                
                -- Add description as a StringValue
                local description = Instance.new("StringValue")
                description.Name = "Description"
                description.Value = eventDef.description
                description.Parent = remoteObject
                
                print("Created " .. eventDef.type .. ": " .. eventDef.name)
            end
        end
    end
    
    print("Remote Events initialized!")
end

-- Helper functions to get remote events
function GameEvents:GetRemoteEvent(eventName)
    return remoteEventsFolder:FindFirstChild(eventName)
end

function GameEvents:GetRemoteFunction(functionName)
    return remoteEventsFolder:FindFirstChild(functionName)
end

-- Event firing helpers (for server use)
function GameEvents:FireClient(player, eventName, ...)
    local remoteEvent = self:GetRemoteEvent(eventName)
    if remoteEvent then
        remoteEvent:FireClient(player, ...)
    else
        warn("RemoteEvent not found: " .. eventName)
    end
end

function GameEvents:FireAllClients(eventName, ...)
    local remoteEvent = self:GetRemoteEvent(eventName)
    if remoteEvent then
        remoteEvent:FireAllClients(...)
    else
        warn("RemoteEvent not found: " .. eventName)
    end
end

-- Event connection helpers (for client use)
function GameEvents:ConnectToServer(eventName, callback)
    local remoteEvent = self:GetRemoteEvent(eventName)
    if remoteEvent then
        return remoteEvent.OnClientEvent:Connect(callback)
    else
        warn("RemoteEvent not found: " .. eventName)
        return nil
    end
end

-- Server event connection helpers
function GameEvents:OnServerEvent(eventName, callback)
    local remoteEvent = self:GetRemoteEvent(eventName)
    if remoteEvent then
        return remoteEvent.OnServerEvent:Connect(callback)
    else
        warn("RemoteEvent not found: " .. eventName)
        return nil
    end
end

-- Remote function helpers
function GameEvents:InvokeServer(functionName, ...)
    local remoteFunction = self:GetRemoteFunction(functionName)
    if remoteFunction then
        return remoteFunction:InvokeServer(...)
    else
        warn("RemoteFunction not found: " .. functionName)
        return nil
    end
end

function GameEvents:InvokeClient(player, functionName, ...)
    local remoteFunction = self:GetRemoteFunction(functionName)
    if remoteFunction then
        return remoteFunction:InvokeClient(player, ...)
    else
        warn("RemoteFunction not found: " .. functionName)
        return nil
    end
end

-- Validation helpers
function GameEvents:ValidateEventData(eventName, data)
    -- Basic validation - can be expanded based on specific event requirements
    if not eventName or type(eventName) ~= "string" then
        return false, "Invalid event name"
    end
    
    local remoteEvent = self:GetRemoteEvent(eventName)
    if not remoteEvent then
        return false, "Event does not exist: " .. eventName
    end
    
    -- Add specific validation rules here based on event type
    return true, "Valid"
end

-- Security helpers
function GameEvents:IsPlayerAuthorized(player, action)
    -- Basic authorization check - expand based on game requirements
    if not player or not player.Parent then
        return false
    end
    
    -- Add role-based or permission-based checks here
    return true
end

-- Rate limiting helpers
local rateLimits = {}
function GameEvents:CheckRateLimit(player, eventName, maxPerSecond)
    maxPerSecond = maxPerSecond or 10 -- Default rate limit
    
    local playerId = player.UserId
    local currentTime = tick()
    
    if not rateLimits[playerId] then
        rateLimits[playerId] = {}
    end
    
    if not rateLimits[playerId][eventName] then
        rateLimits[playerId][eventName] = {count = 0, lastReset = currentTime}
    end
    
    local limit = rateLimits[playerId][eventName]
    
    -- Reset counter if a second has passed
    if currentTime - limit.lastReset >= 1 then
        limit.count = 0
        limit.lastReset = currentTime
    end
    
    -- Check if under limit
    if limit.count < maxPerSecond then
        limit.count = limit.count + 1
        return true
    else
        return false
    end
end

-- Cleanup function
function GameEvents:Cleanup()
    -- Clean up rate limit data for disconnected players
    local connectedPlayers = {}
    for _, player in pairs(game.Players:GetPlayers()) do
        connectedPlayers[player.UserId] = true
    end
    
    for playerId, _ in pairs(rateLimits) do
        if not connectedPlayers[playerId] then
            rateLimits[playerId] = nil
        end
    end
end

-- Initialize events when module is loaded
GameEvents:Initialize()

-- Clean up disconnected players periodically
spawn(function()
    while true do
        wait(60) -- Clean up every minute
        GameEvents:Cleanup()
    end
end)

return GameEvents
